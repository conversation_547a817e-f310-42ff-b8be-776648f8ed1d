function G0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Tp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Np={exports:{}},ho={},jp={exports:{}},O={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ai=Symbol.for("react.element"),Y0=Symbol.for("react.portal"),Q0=Symbol.for("react.fragment"),X0=Symbol.for("react.strict_mode"),q0=Symbol.for("react.profiler"),Z0=Symbol.for("react.provider"),J0=Symbol.for("react.context"),ey=Symbol.for("react.forward_ref"),ty=Symbol.for("react.suspense"),ny=Symbol.for("react.memo"),ry=Symbol.for("react.lazy"),Uc=Symbol.iterator;function iy(e){return e===null||typeof e!="object"?null:(e=Uc&&e[Uc]||e["@@iterator"],typeof e=="function"?e:null)}var Mp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ap=Object.assign,Lp={};function Er(e,t,n){this.props=e,this.context=t,this.refs=Lp,this.updater=n||Mp}Er.prototype.isReactComponent={};Er.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Er.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Dp(){}Dp.prototype=Er.prototype;function fu(e,t,n){this.props=e,this.context=t,this.refs=Lp,this.updater=n||Mp}var pu=fu.prototype=new Dp;pu.constructor=fu;Ap(pu,Er.prototype);pu.isPureReactComponent=!0;var Hc=Array.isArray,Rp=Object.prototype.hasOwnProperty,hu={current:null},bp={key:!0,ref:!0,__self:!0,__source:!0};function Vp(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)Rp.call(t,r)&&!bp.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Ai,type:e,key:s,ref:o,props:i,_owner:hu.current}}function sy(e,t){return{$$typeof:Ai,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function mu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ai}function oy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Wc=/\/+/g;function Uo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?oy(""+e.key):t.toString(36)}function ps(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Ai:case Y0:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Uo(o,0):r,Hc(i)?(n="",e!=null&&(n=e.replace(Wc,"$&/")+"/"),ps(i,t,n,"",function(u){return u})):i!=null&&(mu(i)&&(i=sy(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Wc,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",Hc(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+Uo(s,a);o+=ps(s,t,n,l,i)}else if(l=iy(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+Uo(s,a++),o+=ps(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Ui(e,t,n){if(e==null)return e;var r=[],i=0;return ps(e,r,"","",function(s){return t.call(n,s,i++)}),r}function ay(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ne={current:null},hs={transition:null},ly={ReactCurrentDispatcher:Ne,ReactCurrentBatchConfig:hs,ReactCurrentOwner:hu};function _p(){throw Error("act(...) is not supported in production builds of React.")}O.Children={map:Ui,forEach:function(e,t,n){Ui(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ui(e,function(){t++}),t},toArray:function(e){return Ui(e,function(t){return t})||[]},only:function(e){if(!mu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};O.Component=Er;O.Fragment=Q0;O.Profiler=q0;O.PureComponent=fu;O.StrictMode=X0;O.Suspense=ty;O.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ly;O.act=_p;O.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ap({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=hu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Rp.call(t,l)&&!bp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Ai,type:e.type,key:i,ref:s,props:r,_owner:o}};O.createContext=function(e){return e={$$typeof:J0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Z0,_context:e},e.Consumer=e};O.createElement=Vp;O.createFactory=function(e){var t=Vp.bind(null,e);return t.type=e,t};O.createRef=function(){return{current:null}};O.forwardRef=function(e){return{$$typeof:ey,render:e}};O.isValidElement=mu;O.lazy=function(e){return{$$typeof:ry,_payload:{_status:-1,_result:e},_init:ay}};O.memo=function(e,t){return{$$typeof:ny,type:e,compare:t===void 0?null:t}};O.startTransition=function(e){var t=hs.transition;hs.transition={};try{e()}finally{hs.transition=t}};O.unstable_act=_p;O.useCallback=function(e,t){return Ne.current.useCallback(e,t)};O.useContext=function(e){return Ne.current.useContext(e)};O.useDebugValue=function(){};O.useDeferredValue=function(e){return Ne.current.useDeferredValue(e)};O.useEffect=function(e,t){return Ne.current.useEffect(e,t)};O.useId=function(){return Ne.current.useId()};O.useImperativeHandle=function(e,t,n){return Ne.current.useImperativeHandle(e,t,n)};O.useInsertionEffect=function(e,t){return Ne.current.useInsertionEffect(e,t)};O.useLayoutEffect=function(e,t){return Ne.current.useLayoutEffect(e,t)};O.useMemo=function(e,t){return Ne.current.useMemo(e,t)};O.useReducer=function(e,t,n){return Ne.current.useReducer(e,t,n)};O.useRef=function(e){return Ne.current.useRef(e)};O.useState=function(e){return Ne.current.useState(e)};O.useSyncExternalStore=function(e,t,n){return Ne.current.useSyncExternalStore(e,t,n)};O.useTransition=function(){return Ne.current.useTransition()};O.version="18.3.1";jp.exports=O;var w=jp.exports;const _=Tp(w),oi=G0({__proto__:null,default:_},[w]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uy=w,cy=Symbol.for("react.element"),dy=Symbol.for("react.fragment"),fy=Object.prototype.hasOwnProperty,py=uy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,hy={key:!0,ref:!0,__self:!0,__source:!0};function Op(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)fy.call(t,r)&&!hy.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:cy,type:e,key:s,ref:o,props:i,_owner:py.current}}ho.Fragment=dy;ho.jsx=Op;ho.jsxs=Op;Np.exports=ho;var d=Np.exports,$p={exports:{}},ze={},Fp={exports:{}},Ip={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,D){var R=N.length;N.push(D);e:for(;0<R;){var U=R-1>>>1,H=N[U];if(0<i(H,D))N[U]=D,N[R]=H,R=U;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var D=N[0],R=N.pop();if(R!==D){N[0]=R;e:for(var U=0,H=N.length,hn=H>>>1;U<hn;){var ue=2*(U+1)-1,zi=N[ue],vt=ue+1,zn=N[vt];if(0>i(zi,R))vt<H&&0>i(zn,zi)?(N[U]=zn,N[vt]=R,U=vt):(N[U]=zi,N[ue]=R,U=ue);else if(vt<H&&0>i(zn,R))N[U]=zn,N[vt]=R,U=vt;else break e}}return D}function i(N,D){var R=N.sortIndex-D.sortIndex;return R!==0?R:N.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,p=null,f=3,y=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(N){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=N)r(u),D.sortIndex=D.expirationTime,t(l,D);else break;D=n(u)}}function k(N){if(x=!1,g(N),!v)if(n(l)!==null)v=!0,$t(E);else{var D=n(u);D!==null&&B(k,D.startTime-N)}}function E(N,D){v=!1,x&&(x=!1,m(C),C=-1),y=!0;var R=f;try{for(g(D),p=n(l);p!==null&&(!(p.expirationTime>D)||N&&!Y());){var U=p.callback;if(typeof U=="function"){p.callback=null,f=p.priorityLevel;var H=U(p.expirationTime<=D);D=e.unstable_now(),typeof H=="function"?p.callback=H:p===n(l)&&r(l),g(D)}else r(l);p=n(l)}if(p!==null)var hn=!0;else{var ue=n(u);ue!==null&&B(k,ue.startTime-D),hn=!1}return hn}finally{p=null,f=R,y=!1}}var T=!1,j=null,C=-1,b=5,A=-1;function Y(){return!(e.unstable_now()-A<b)}function Ce(){if(j!==null){var N=e.unstable_now();A=N;var D=!0;try{D=j(!0,N)}finally{D?be():(T=!1,j=null)}}else T=!1}var be;if(typeof h=="function")be=function(){h(Ce)};else if(typeof MessageChannel<"u"){var tt=new MessageChannel,ct=tt.port2;tt.port1.onmessage=Ce,be=function(){ct.postMessage(null)}}else be=function(){S(Ce,0)};function $t(N){j=N,T||(T=!0,be())}function B(N,D){C=S(function(){N(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,$t(E))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):b=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(f){case 1:case 2:case 3:var D=3;break;default:D=f}var R=f;f=D;try{return N()}finally{f=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,D){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var R=f;f=N;try{return D()}finally{f=R}},e.unstable_scheduleCallback=function(N,D,R){var U=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?U+R:U):R=U,N){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=R+H,N={id:c++,callback:D,priorityLevel:N,startTime:R,expirationTime:H,sortIndex:-1},R>U?(N.sortIndex=R,t(u,N),n(l)===null&&N===n(u)&&(x?(m(C),C=-1):x=!0,B(k,R-U))):(N.sortIndex=H,t(l,N),v||y||(v=!0,$t(E))),N},e.unstable_shouldYield=Y,e.unstable_wrapCallback=function(N){var D=f;return function(){var R=f;f=D;try{return N.apply(this,arguments)}finally{f=R}}}})(Ip);Fp.exports=Ip;var my=Fp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gy=w,Fe=my;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var zp=new Set,ai={};function _n(e,t){fr(e,t),fr(e+"Capture",t)}function fr(e,t){for(ai[e]=t,e=0;e<t.length;e++)zp.add(t[e])}var Rt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ia=Object.prototype.hasOwnProperty,yy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Kc={},Gc={};function vy(e){return Ia.call(Gc,e)?!0:Ia.call(Kc,e)?!1:yy.test(e)?Gc[e]=!0:(Kc[e]=!0,!1)}function xy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function wy(e,t,n,r){if(t===null||typeof t>"u"||xy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var me={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){me[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];me[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){me[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){me[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){me[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){me[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){me[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){me[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){me[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var gu=/[\-:]([a-z])/g;function yu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(gu,yu);me[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(gu,yu);me[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(gu,yu);me[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){me[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});me.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){me[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function vu(e,t,n,r){var i=me.hasOwnProperty(t)?me[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(wy(t,n,i,r)&&(n=null),r||i===null?vy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Ot=gy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Hi=Symbol.for("react.element"),Un=Symbol.for("react.portal"),Hn=Symbol.for("react.fragment"),xu=Symbol.for("react.strict_mode"),za=Symbol.for("react.profiler"),Bp=Symbol.for("react.provider"),Up=Symbol.for("react.context"),wu=Symbol.for("react.forward_ref"),Ba=Symbol.for("react.suspense"),Ua=Symbol.for("react.suspense_list"),Su=Symbol.for("react.memo"),Bt=Symbol.for("react.lazy"),Hp=Symbol.for("react.offscreen"),Yc=Symbol.iterator;function Ar(e){return e===null||typeof e!="object"?null:(e=Yc&&e[Yc]||e["@@iterator"],typeof e=="function"?e:null)}var ee=Object.assign,Ho;function zr(e){if(Ho===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ho=t&&t[1]||""}return`
`+Ho+e}var Wo=!1;function Ko(e,t){if(!e||Wo)return"";Wo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{Wo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?zr(e):""}function Sy(e){switch(e.tag){case 5:return zr(e.type);case 16:return zr("Lazy");case 13:return zr("Suspense");case 19:return zr("SuspenseList");case 0:case 2:case 15:return e=Ko(e.type,!1),e;case 11:return e=Ko(e.type.render,!1),e;case 1:return e=Ko(e.type,!0),e;default:return""}}function Ha(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Hn:return"Fragment";case Un:return"Portal";case za:return"Profiler";case xu:return"StrictMode";case Ba:return"Suspense";case Ua:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Up:return(e.displayName||"Context")+".Consumer";case Bp:return(e._context.displayName||"Context")+".Provider";case wu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Su:return t=e.displayName||null,t!==null?t:Ha(e.type)||"Memo";case Bt:t=e._payload,e=e._init;try{return Ha(e(t))}catch{}}return null}function ky(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ha(t);case 8:return t===xu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function sn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Wp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ey(e){var t=Wp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Wi(e){e._valueTracker||(e._valueTracker=Ey(e))}function Kp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Wp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ms(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Wa(e,t){var n=t.checked;return ee({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Qc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=sn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Gp(e,t){t=t.checked,t!=null&&vu(e,"checked",t,!1)}function Ka(e,t){Gp(e,t);var n=sn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ga(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ga(e,t.type,sn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Xc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ga(e,t,n){(t!=="number"||Ms(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Br=Array.isArray;function or(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+sn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ya(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return ee({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function qc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Br(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:sn(n)}}function Yp(e,t){var n=sn(t.value),r=sn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Zc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Qp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Qa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Qp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ki,Xp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ki=Ki||document.createElement("div"),Ki.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ki.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function li(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Yr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Cy=["Webkit","ms","Moz","O"];Object.keys(Yr).forEach(function(e){Cy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Yr[t]=Yr[e]})});function qp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Yr.hasOwnProperty(e)&&Yr[e]?(""+t).trim():t+"px"}function Zp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=qp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Py=ee({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Xa(e,t){if(t){if(Py[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function qa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Za=null;function ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ja=null,ar=null,lr=null;function Jc(e){if(e=Ri(e)){if(typeof Ja!="function")throw Error(P(280));var t=e.stateNode;t&&(t=xo(t),Ja(e.stateNode,e.type,t))}}function Jp(e){ar?lr?lr.push(e):lr=[e]:ar=e}function eh(){if(ar){var e=ar,t=lr;if(lr=ar=null,Jc(e),t)for(e=0;e<t.length;e++)Jc(t[e])}}function th(e,t){return e(t)}function nh(){}var Go=!1;function rh(e,t,n){if(Go)return e(t,n);Go=!0;try{return th(e,t,n)}finally{Go=!1,(ar!==null||lr!==null)&&(nh(),eh())}}function ui(e,t){var n=e.stateNode;if(n===null)return null;var r=xo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var el=!1;if(Rt)try{var Lr={};Object.defineProperty(Lr,"passive",{get:function(){el=!0}}),window.addEventListener("test",Lr,Lr),window.removeEventListener("test",Lr,Lr)}catch{el=!1}function Ty(e,t,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Qr=!1,As=null,Ls=!1,tl=null,Ny={onError:function(e){Qr=!0,As=e}};function jy(e,t,n,r,i,s,o,a,l){Qr=!1,As=null,Ty.apply(Ny,arguments)}function My(e,t,n,r,i,s,o,a,l){if(jy.apply(this,arguments),Qr){if(Qr){var u=As;Qr=!1,As=null}else throw Error(P(198));Ls||(Ls=!0,tl=u)}}function On(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ih(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ed(e){if(On(e)!==e)throw Error(P(188))}function Ay(e){var t=e.alternate;if(!t){if(t=On(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return ed(i),e;if(s===r)return ed(i),t;s=s.sibling}throw Error(P(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function sh(e){return e=Ay(e),e!==null?oh(e):null}function oh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=oh(e);if(t!==null)return t;e=e.sibling}return null}var ah=Fe.unstable_scheduleCallback,td=Fe.unstable_cancelCallback,Ly=Fe.unstable_shouldYield,Dy=Fe.unstable_requestPaint,se=Fe.unstable_now,Ry=Fe.unstable_getCurrentPriorityLevel,Eu=Fe.unstable_ImmediatePriority,lh=Fe.unstable_UserBlockingPriority,Ds=Fe.unstable_NormalPriority,by=Fe.unstable_LowPriority,uh=Fe.unstable_IdlePriority,mo=null,ht=null;function Vy(e){if(ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(mo,e,void 0,(e.current.flags&128)===128)}catch{}}var ot=Math.clz32?Math.clz32:$y,_y=Math.log,Oy=Math.LN2;function $y(e){return e>>>=0,e===0?32:31-(_y(e)/Oy|0)|0}var Gi=64,Yi=4194304;function Ur(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Rs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=Ur(a):(s&=o,s!==0&&(r=Ur(s)))}else o=n&~i,o!==0?r=Ur(o):s!==0&&(r=Ur(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ot(t),i=1<<n,r|=e[n],t&=~i;return r}function Fy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Iy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-ot(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=Fy(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function nl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ch(){var e=Gi;return Gi<<=1,!(Gi&4194240)&&(Gi=64),e}function Yo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Li(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ot(t),e[t]=n}function zy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-ot(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Cu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var I=0;function dh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var fh,Pu,ph,hh,mh,rl=!1,Qi=[],Xt=null,qt=null,Zt=null,ci=new Map,di=new Map,Ht=[],By="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function nd(e,t){switch(e){case"focusin":case"focusout":Xt=null;break;case"dragenter":case"dragleave":qt=null;break;case"mouseover":case"mouseout":Zt=null;break;case"pointerover":case"pointerout":ci.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":di.delete(t.pointerId)}}function Dr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=Ri(t),t!==null&&Pu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Uy(e,t,n,r,i){switch(t){case"focusin":return Xt=Dr(Xt,e,t,n,r,i),!0;case"dragenter":return qt=Dr(qt,e,t,n,r,i),!0;case"mouseover":return Zt=Dr(Zt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return ci.set(s,Dr(ci.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,di.set(s,Dr(di.get(s)||null,e,t,n,r,i)),!0}return!1}function gh(e){var t=Sn(e.target);if(t!==null){var n=On(t);if(n!==null){if(t=n.tag,t===13){if(t=ih(n),t!==null){e.blockedOn=t,mh(e.priority,function(){ph(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ms(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=il(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Za=r,n.target.dispatchEvent(r),Za=null}else return t=Ri(n),t!==null&&Pu(t),e.blockedOn=n,!1;t.shift()}return!0}function rd(e,t,n){ms(e)&&n.delete(t)}function Hy(){rl=!1,Xt!==null&&ms(Xt)&&(Xt=null),qt!==null&&ms(qt)&&(qt=null),Zt!==null&&ms(Zt)&&(Zt=null),ci.forEach(rd),di.forEach(rd)}function Rr(e,t){e.blockedOn===t&&(e.blockedOn=null,rl||(rl=!0,Fe.unstable_scheduleCallback(Fe.unstable_NormalPriority,Hy)))}function fi(e){function t(i){return Rr(i,e)}if(0<Qi.length){Rr(Qi[0],e);for(var n=1;n<Qi.length;n++){var r=Qi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Xt!==null&&Rr(Xt,e),qt!==null&&Rr(qt,e),Zt!==null&&Rr(Zt,e),ci.forEach(t),di.forEach(t),n=0;n<Ht.length;n++)r=Ht[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ht.length&&(n=Ht[0],n.blockedOn===null);)gh(n),n.blockedOn===null&&Ht.shift()}var ur=Ot.ReactCurrentBatchConfig,bs=!0;function Wy(e,t,n,r){var i=I,s=ur.transition;ur.transition=null;try{I=1,Tu(e,t,n,r)}finally{I=i,ur.transition=s}}function Ky(e,t,n,r){var i=I,s=ur.transition;ur.transition=null;try{I=4,Tu(e,t,n,r)}finally{I=i,ur.transition=s}}function Tu(e,t,n,r){if(bs){var i=il(e,t,n,r);if(i===null)ia(e,t,r,Vs,n),nd(e,r);else if(Uy(i,e,t,n,r))r.stopPropagation();else if(nd(e,r),t&4&&-1<By.indexOf(e)){for(;i!==null;){var s=Ri(i);if(s!==null&&fh(s),s=il(e,t,n,r),s===null&&ia(e,t,r,Vs,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else ia(e,t,r,null,n)}}var Vs=null;function il(e,t,n,r){if(Vs=null,e=ku(r),e=Sn(e),e!==null)if(t=On(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ih(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Vs=e,null}function yh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ry()){case Eu:return 1;case lh:return 4;case Ds:case by:return 16;case uh:return 536870912;default:return 16}default:return 16}}var Gt=null,Nu=null,gs=null;function vh(){if(gs)return gs;var e,t=Nu,n=t.length,r,i="value"in Gt?Gt.value:Gt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return gs=i.slice(e,1<r?1-r:void 0)}function ys(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Xi(){return!0}function id(){return!1}function Be(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Xi:id,this.isPropagationStopped=id,this}return ee(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Xi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Xi)},persist:function(){},isPersistent:Xi}),t}var Cr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ju=Be(Cr),Di=ee({},Cr,{view:0,detail:0}),Gy=Be(Di),Qo,Xo,br,go=ee({},Di,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==br&&(br&&e.type==="mousemove"?(Qo=e.screenX-br.screenX,Xo=e.screenY-br.screenY):Xo=Qo=0,br=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:Xo}}),sd=Be(go),Yy=ee({},go,{dataTransfer:0}),Qy=Be(Yy),Xy=ee({},Di,{relatedTarget:0}),qo=Be(Xy),qy=ee({},Cr,{animationName:0,elapsedTime:0,pseudoElement:0}),Zy=Be(qy),Jy=ee({},Cr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ev=Be(Jy),tv=ee({},Cr,{data:0}),od=Be(tv),nv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},rv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},iv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=iv[e])?!!t[e]:!1}function Mu(){return sv}var ov=ee({},Di,{key:function(e){if(e.key){var t=nv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ys(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?rv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mu,charCode:function(e){return e.type==="keypress"?ys(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ys(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),av=Be(ov),lv=ee({},go,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ad=Be(lv),uv=ee({},Di,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mu}),cv=Be(uv),dv=ee({},Cr,{propertyName:0,elapsedTime:0,pseudoElement:0}),fv=Be(dv),pv=ee({},go,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),hv=Be(pv),mv=[9,13,27,32],Au=Rt&&"CompositionEvent"in window,Xr=null;Rt&&"documentMode"in document&&(Xr=document.documentMode);var gv=Rt&&"TextEvent"in window&&!Xr,xh=Rt&&(!Au||Xr&&8<Xr&&11>=Xr),ld=" ",ud=!1;function wh(e,t){switch(e){case"keyup":return mv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wn=!1;function yv(e,t){switch(e){case"compositionend":return Sh(t);case"keypress":return t.which!==32?null:(ud=!0,ld);case"textInput":return e=t.data,e===ld&&ud?null:e;default:return null}}function vv(e,t){if(Wn)return e==="compositionend"||!Au&&wh(e,t)?(e=vh(),gs=Nu=Gt=null,Wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return xh&&t.locale!=="ko"?null:t.data;default:return null}}var xv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!xv[e.type]:t==="textarea"}function kh(e,t,n,r){Jp(r),t=_s(t,"onChange"),0<t.length&&(n=new ju("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qr=null,pi=null;function wv(e){Rh(e,0)}function yo(e){var t=Yn(e);if(Kp(t))return e}function Sv(e,t){if(e==="change")return t}var Eh=!1;if(Rt){var Zo;if(Rt){var Jo="oninput"in document;if(!Jo){var dd=document.createElement("div");dd.setAttribute("oninput","return;"),Jo=typeof dd.oninput=="function"}Zo=Jo}else Zo=!1;Eh=Zo&&(!document.documentMode||9<document.documentMode)}function fd(){qr&&(qr.detachEvent("onpropertychange",Ch),pi=qr=null)}function Ch(e){if(e.propertyName==="value"&&yo(pi)){var t=[];kh(t,pi,e,ku(e)),rh(wv,t)}}function kv(e,t,n){e==="focusin"?(fd(),qr=t,pi=n,qr.attachEvent("onpropertychange",Ch)):e==="focusout"&&fd()}function Ev(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return yo(pi)}function Cv(e,t){if(e==="click")return yo(t)}function Pv(e,t){if(e==="input"||e==="change")return yo(t)}function Tv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var lt=typeof Object.is=="function"?Object.is:Tv;function hi(e,t){if(lt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Ia.call(t,i)||!lt(e[i],t[i]))return!1}return!0}function pd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function hd(e,t){var n=pd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=pd(n)}}function Ph(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ph(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Th(){for(var e=window,t=Ms();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ms(e.document)}return t}function Lu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Nv(e){var t=Th(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ph(n.ownerDocument.documentElement,n)){if(r!==null&&Lu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=hd(n,s);var o=hd(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var jv=Rt&&"documentMode"in document&&11>=document.documentMode,Kn=null,sl=null,Zr=null,ol=!1;function md(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ol||Kn==null||Kn!==Ms(r)||(r=Kn,"selectionStart"in r&&Lu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Zr&&hi(Zr,r)||(Zr=r,r=_s(sl,"onSelect"),0<r.length&&(t=new ju("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Kn)))}function qi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Gn={animationend:qi("Animation","AnimationEnd"),animationiteration:qi("Animation","AnimationIteration"),animationstart:qi("Animation","AnimationStart"),transitionend:qi("Transition","TransitionEnd")},ea={},Nh={};Rt&&(Nh=document.createElement("div").style,"AnimationEvent"in window||(delete Gn.animationend.animation,delete Gn.animationiteration.animation,delete Gn.animationstart.animation),"TransitionEvent"in window||delete Gn.transitionend.transition);function vo(e){if(ea[e])return ea[e];if(!Gn[e])return e;var t=Gn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Nh)return ea[e]=t[n];return e}var jh=vo("animationend"),Mh=vo("animationiteration"),Ah=vo("animationstart"),Lh=vo("transitionend"),Dh=new Map,gd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function cn(e,t){Dh.set(e,t),_n(t,[e])}for(var ta=0;ta<gd.length;ta++){var na=gd[ta],Mv=na.toLowerCase(),Av=na[0].toUpperCase()+na.slice(1);cn(Mv,"on"+Av)}cn(jh,"onAnimationEnd");cn(Mh,"onAnimationIteration");cn(Ah,"onAnimationStart");cn("dblclick","onDoubleClick");cn("focusin","onFocus");cn("focusout","onBlur");cn(Lh,"onTransitionEnd");fr("onMouseEnter",["mouseout","mouseover"]);fr("onMouseLeave",["mouseout","mouseover"]);fr("onPointerEnter",["pointerout","pointerover"]);fr("onPointerLeave",["pointerout","pointerover"]);_n("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));_n("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));_n("onBeforeInput",["compositionend","keypress","textInput","paste"]);_n("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));_n("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));_n("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hr));function yd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,My(r,t,void 0,e),e.currentTarget=null}function Rh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;yd(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;yd(i,a,u),s=l}}}if(Ls)throw e=tl,Ls=!1,tl=null,e}function K(e,t){var n=t[dl];n===void 0&&(n=t[dl]=new Set);var r=e+"__bubble";n.has(r)||(bh(t,e,2,!1),n.add(r))}function ra(e,t,n){var r=0;t&&(r|=4),bh(n,e,r,t)}var Zi="_reactListening"+Math.random().toString(36).slice(2);function mi(e){if(!e[Zi]){e[Zi]=!0,zp.forEach(function(n){n!=="selectionchange"&&(Lv.has(n)||ra(n,!1,e),ra(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Zi]||(t[Zi]=!0,ra("selectionchange",!1,t))}}function bh(e,t,n,r){switch(yh(t)){case 1:var i=Wy;break;case 4:i=Ky;break;default:i=Tu}n=i.bind(null,t,n,e),i=void 0,!el||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function ia(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=Sn(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}rh(function(){var u=s,c=ku(n),p=[];e:{var f=Dh.get(e);if(f!==void 0){var y=ju,v=e;switch(e){case"keypress":if(ys(n)===0)break e;case"keydown":case"keyup":y=av;break;case"focusin":v="focus",y=qo;break;case"focusout":v="blur",y=qo;break;case"beforeblur":case"afterblur":y=qo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=sd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Qy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=cv;break;case jh:case Mh:case Ah:y=Zy;break;case Lh:y=fv;break;case"scroll":y=Gy;break;case"wheel":y=hv;break;case"copy":case"cut":case"paste":y=ev;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=ad}var x=(t&4)!==0,S=!x&&e==="scroll",m=x?f!==null?f+"Capture":null:f;x=[];for(var h=u,g;h!==null;){g=h;var k=g.stateNode;if(g.tag===5&&k!==null&&(g=k,m!==null&&(k=ui(h,m),k!=null&&x.push(gi(h,k,g)))),S)break;h=h.return}0<x.length&&(f=new y(f,v,null,n,c),p.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",f&&n!==Za&&(v=n.relatedTarget||n.fromElement)&&(Sn(v)||v[bt]))break e;if((y||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,y?(v=n.relatedTarget||n.toElement,y=u,v=v?Sn(v):null,v!==null&&(S=On(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=u),y!==v)){if(x=sd,k="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=ad,k="onPointerLeave",m="onPointerEnter",h="pointer"),S=y==null?f:Yn(y),g=v==null?f:Yn(v),f=new x(k,h+"leave",y,n,c),f.target=S,f.relatedTarget=g,k=null,Sn(c)===u&&(x=new x(m,h+"enter",v,n,c),x.target=g,x.relatedTarget=S,k=x),S=k,y&&v)t:{for(x=y,m=v,h=0,g=x;g;g=Bn(g))h++;for(g=0,k=m;k;k=Bn(k))g++;for(;0<h-g;)x=Bn(x),h--;for(;0<g-h;)m=Bn(m),g--;for(;h--;){if(x===m||m!==null&&x===m.alternate)break t;x=Bn(x),m=Bn(m)}x=null}else x=null;y!==null&&vd(p,f,y,x,!1),v!==null&&S!==null&&vd(p,S,v,x,!0)}}e:{if(f=u?Yn(u):window,y=f.nodeName&&f.nodeName.toLowerCase(),y==="select"||y==="input"&&f.type==="file")var E=Sv;else if(cd(f))if(Eh)E=Pv;else{E=Ev;var T=kv}else(y=f.nodeName)&&y.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(E=Cv);if(E&&(E=E(e,u))){kh(p,E,n,c);break e}T&&T(e,f,u),e==="focusout"&&(T=f._wrapperState)&&T.controlled&&f.type==="number"&&Ga(f,"number",f.value)}switch(T=u?Yn(u):window,e){case"focusin":(cd(T)||T.contentEditable==="true")&&(Kn=T,sl=u,Zr=null);break;case"focusout":Zr=sl=Kn=null;break;case"mousedown":ol=!0;break;case"contextmenu":case"mouseup":case"dragend":ol=!1,md(p,n,c);break;case"selectionchange":if(jv)break;case"keydown":case"keyup":md(p,n,c)}var j;if(Au)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else Wn?wh(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(xh&&n.locale!=="ko"&&(Wn||C!=="onCompositionStart"?C==="onCompositionEnd"&&Wn&&(j=vh()):(Gt=c,Nu="value"in Gt?Gt.value:Gt.textContent,Wn=!0)),T=_s(u,C),0<T.length&&(C=new od(C,e,null,n,c),p.push({event:C,listeners:T}),j?C.data=j:(j=Sh(n),j!==null&&(C.data=j)))),(j=gv?yv(e,n):vv(e,n))&&(u=_s(u,"onBeforeInput"),0<u.length&&(c=new od("onBeforeInput","beforeinput",null,n,c),p.push({event:c,listeners:u}),c.data=j))}Rh(p,t)})}function gi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function _s(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=ui(e,n),s!=null&&r.unshift(gi(e,s,i)),s=ui(e,t),s!=null&&r.push(gi(e,s,i))),e=e.return}return r}function Bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function vd(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=ui(n,s),l!=null&&o.unshift(gi(n,l,a))):i||(l=ui(n,s),l!=null&&o.push(gi(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Dv=/\r\n?/g,Rv=/\u0000|\uFFFD/g;function xd(e){return(typeof e=="string"?e:""+e).replace(Dv,`
`).replace(Rv,"")}function Ji(e,t,n){if(t=xd(t),xd(e)!==t&&n)throw Error(P(425))}function Os(){}var al=null,ll=null;function ul(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var cl=typeof setTimeout=="function"?setTimeout:void 0,bv=typeof clearTimeout=="function"?clearTimeout:void 0,wd=typeof Promise=="function"?Promise:void 0,Vv=typeof queueMicrotask=="function"?queueMicrotask:typeof wd<"u"?function(e){return wd.resolve(null).then(e).catch(_v)}:cl;function _v(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),fi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);fi(t)}function Jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Sd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Pr=Math.random().toString(36).slice(2),pt="__reactFiber$"+Pr,yi="__reactProps$"+Pr,bt="__reactContainer$"+Pr,dl="__reactEvents$"+Pr,Ov="__reactListeners$"+Pr,$v="__reactHandles$"+Pr;function Sn(e){var t=e[pt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[bt]||n[pt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Sd(e);e!==null;){if(n=e[pt])return n;e=Sd(e)}return t}e=n,n=e.parentNode}return null}function Ri(e){return e=e[pt]||e[bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Yn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function xo(e){return e[yi]||null}var fl=[],Qn=-1;function dn(e){return{current:e}}function G(e){0>Qn||(e.current=fl[Qn],fl[Qn]=null,Qn--)}function W(e,t){Qn++,fl[Qn]=e.current,e.current=t}var on={},Ee=dn(on),Le=dn(!1),Ln=on;function pr(e,t){var n=e.type.contextTypes;if(!n)return on;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function De(e){return e=e.childContextTypes,e!=null}function $s(){G(Le),G(Ee)}function kd(e,t,n){if(Ee.current!==on)throw Error(P(168));W(Ee,t),W(Le,n)}function Vh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(P(108,ky(e)||"Unknown",i));return ee({},n,r)}function Fs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||on,Ln=Ee.current,W(Ee,e),W(Le,Le.current),!0}function Ed(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=Vh(e,t,Ln),r.__reactInternalMemoizedMergedChildContext=e,G(Le),G(Ee),W(Ee,e)):G(Le),W(Le,n)}var kt=null,wo=!1,oa=!1;function _h(e){kt===null?kt=[e]:kt.push(e)}function Fv(e){wo=!0,_h(e)}function fn(){if(!oa&&kt!==null){oa=!0;var e=0,t=I;try{var n=kt;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}kt=null,wo=!1}catch(i){throw kt!==null&&(kt=kt.slice(e+1)),ah(Eu,fn),i}finally{I=t,oa=!1}}return null}var Xn=[],qn=0,Is=null,zs=0,Ge=[],Ye=0,Dn=null,Et=1,Ct="";function gn(e,t){Xn[qn++]=zs,Xn[qn++]=Is,Is=e,zs=t}function Oh(e,t,n){Ge[Ye++]=Et,Ge[Ye++]=Ct,Ge[Ye++]=Dn,Dn=e;var r=Et;e=Ct;var i=32-ot(r)-1;r&=~(1<<i),n+=1;var s=32-ot(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Et=1<<32-ot(t)+i|n<<i|r,Ct=s+e}else Et=1<<s|n<<i|r,Ct=e}function Du(e){e.return!==null&&(gn(e,1),Oh(e,1,0))}function Ru(e){for(;e===Is;)Is=Xn[--qn],Xn[qn]=null,zs=Xn[--qn],Xn[qn]=null;for(;e===Dn;)Dn=Ge[--Ye],Ge[Ye]=null,Ct=Ge[--Ye],Ge[Ye]=null,Et=Ge[--Ye],Ge[Ye]=null}var $e=null,Oe=null,Q=!1,st=null;function $h(e,t){var n=Qe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Cd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,$e=e,Oe=Jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,$e=e,Oe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Dn!==null?{id:Et,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Qe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,$e=e,Oe=null,!0):!1;default:return!1}}function pl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function hl(e){if(Q){var t=Oe;if(t){var n=t;if(!Cd(e,t)){if(pl(e))throw Error(P(418));t=Jt(n.nextSibling);var r=$e;t&&Cd(e,t)?$h(r,n):(e.flags=e.flags&-4097|2,Q=!1,$e=e)}}else{if(pl(e))throw Error(P(418));e.flags=e.flags&-4097|2,Q=!1,$e=e}}}function Pd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;$e=e}function es(e){if(e!==$e)return!1;if(!Q)return Pd(e),Q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ul(e.type,e.memoizedProps)),t&&(t=Oe)){if(pl(e))throw Fh(),Error(P(418));for(;t;)$h(e,t),t=Jt(t.nextSibling)}if(Pd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Oe=Jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Oe=null}}else Oe=$e?Jt(e.stateNode.nextSibling):null;return!0}function Fh(){for(var e=Oe;e;)e=Jt(e.nextSibling)}function hr(){Oe=$e=null,Q=!1}function bu(e){st===null?st=[e]:st.push(e)}var Iv=Ot.ReactCurrentBatchConfig;function Vr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function ts(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Td(e){var t=e._init;return t(e._payload)}function Ih(e){function t(m,h){if(e){var g=m.deletions;g===null?(m.deletions=[h],m.flags|=16):g.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function i(m,h){return m=rn(m,h),m.index=0,m.sibling=null,m}function s(m,h,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<h?(m.flags|=2,h):g):(m.flags|=2,h)):(m.flags|=1048576,h)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,h,g,k){return h===null||h.tag!==6?(h=pa(g,m.mode,k),h.return=m,h):(h=i(h,g),h.return=m,h)}function l(m,h,g,k){var E=g.type;return E===Hn?c(m,h,g.props.children,k,g.key):h!==null&&(h.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Bt&&Td(E)===h.type)?(k=i(h,g.props),k.ref=Vr(m,h,g),k.return=m,k):(k=Cs(g.type,g.key,g.props,null,m.mode,k),k.ref=Vr(m,h,g),k.return=m,k)}function u(m,h,g,k){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=ha(g,m.mode,k),h.return=m,h):(h=i(h,g.children||[]),h.return=m,h)}function c(m,h,g,k,E){return h===null||h.tag!==7?(h=Nn(g,m.mode,k,E),h.return=m,h):(h=i(h,g),h.return=m,h)}function p(m,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=pa(""+h,m.mode,g),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Hi:return g=Cs(h.type,h.key,h.props,null,m.mode,g),g.ref=Vr(m,null,h),g.return=m,g;case Un:return h=ha(h,m.mode,g),h.return=m,h;case Bt:var k=h._init;return p(m,k(h._payload),g)}if(Br(h)||Ar(h))return h=Nn(h,m.mode,g,null),h.return=m,h;ts(m,h)}return null}function f(m,h,g,k){var E=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return E!==null?null:a(m,h,""+g,k);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Hi:return g.key===E?l(m,h,g,k):null;case Un:return g.key===E?u(m,h,g,k):null;case Bt:return E=g._init,f(m,h,E(g._payload),k)}if(Br(g)||Ar(g))return E!==null?null:c(m,h,g,k,null);ts(m,g)}return null}function y(m,h,g,k,E){if(typeof k=="string"&&k!==""||typeof k=="number")return m=m.get(g)||null,a(h,m,""+k,E);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Hi:return m=m.get(k.key===null?g:k.key)||null,l(h,m,k,E);case Un:return m=m.get(k.key===null?g:k.key)||null,u(h,m,k,E);case Bt:var T=k._init;return y(m,h,g,T(k._payload),E)}if(Br(k)||Ar(k))return m=m.get(g)||null,c(h,m,k,E,null);ts(h,k)}return null}function v(m,h,g,k){for(var E=null,T=null,j=h,C=h=0,b=null;j!==null&&C<g.length;C++){j.index>C?(b=j,j=null):b=j.sibling;var A=f(m,j,g[C],k);if(A===null){j===null&&(j=b);break}e&&j&&A.alternate===null&&t(m,j),h=s(A,h,C),T===null?E=A:T.sibling=A,T=A,j=b}if(C===g.length)return n(m,j),Q&&gn(m,C),E;if(j===null){for(;C<g.length;C++)j=p(m,g[C],k),j!==null&&(h=s(j,h,C),T===null?E=j:T.sibling=j,T=j);return Q&&gn(m,C),E}for(j=r(m,j);C<g.length;C++)b=y(j,m,C,g[C],k),b!==null&&(e&&b.alternate!==null&&j.delete(b.key===null?C:b.key),h=s(b,h,C),T===null?E=b:T.sibling=b,T=b);return e&&j.forEach(function(Y){return t(m,Y)}),Q&&gn(m,C),E}function x(m,h,g,k){var E=Ar(g);if(typeof E!="function")throw Error(P(150));if(g=E.call(g),g==null)throw Error(P(151));for(var T=E=null,j=h,C=h=0,b=null,A=g.next();j!==null&&!A.done;C++,A=g.next()){j.index>C?(b=j,j=null):b=j.sibling;var Y=f(m,j,A.value,k);if(Y===null){j===null&&(j=b);break}e&&j&&Y.alternate===null&&t(m,j),h=s(Y,h,C),T===null?E=Y:T.sibling=Y,T=Y,j=b}if(A.done)return n(m,j),Q&&gn(m,C),E;if(j===null){for(;!A.done;C++,A=g.next())A=p(m,A.value,k),A!==null&&(h=s(A,h,C),T===null?E=A:T.sibling=A,T=A);return Q&&gn(m,C),E}for(j=r(m,j);!A.done;C++,A=g.next())A=y(j,m,C,A.value,k),A!==null&&(e&&A.alternate!==null&&j.delete(A.key===null?C:A.key),h=s(A,h,C),T===null?E=A:T.sibling=A,T=A);return e&&j.forEach(function(Ce){return t(m,Ce)}),Q&&gn(m,C),E}function S(m,h,g,k){if(typeof g=="object"&&g!==null&&g.type===Hn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Hi:e:{for(var E=g.key,T=h;T!==null;){if(T.key===E){if(E=g.type,E===Hn){if(T.tag===7){n(m,T.sibling),h=i(T,g.props.children),h.return=m,m=h;break e}}else if(T.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Bt&&Td(E)===T.type){n(m,T.sibling),h=i(T,g.props),h.ref=Vr(m,T,g),h.return=m,m=h;break e}n(m,T);break}else t(m,T);T=T.sibling}g.type===Hn?(h=Nn(g.props.children,m.mode,k,g.key),h.return=m,m=h):(k=Cs(g.type,g.key,g.props,null,m.mode,k),k.ref=Vr(m,h,g),k.return=m,m=k)}return o(m);case Un:e:{for(T=g.key;h!==null;){if(h.key===T)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(m,h.sibling),h=i(h,g.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=ha(g,m.mode,k),h.return=m,m=h}return o(m);case Bt:return T=g._init,S(m,h,T(g._payload),k)}if(Br(g))return v(m,h,g,k);if(Ar(g))return x(m,h,g,k);ts(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(m,h.sibling),h=i(h,g),h.return=m,m=h):(n(m,h),h=pa(g,m.mode,k),h.return=m,m=h),o(m)):n(m,h)}return S}var mr=Ih(!0),zh=Ih(!1),Bs=dn(null),Us=null,Zn=null,Vu=null;function _u(){Vu=Zn=Us=null}function Ou(e){var t=Bs.current;G(Bs),e._currentValue=t}function ml(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function cr(e,t){Us=e,Vu=Zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ae=!0),e.firstContext=null)}function qe(e){var t=e._currentValue;if(Vu!==e)if(e={context:e,memoizedValue:t,next:null},Zn===null){if(Us===null)throw Error(P(308));Zn=e,Us.dependencies={lanes:0,firstContext:e}}else Zn=Zn.next=e;return t}var kn=null;function $u(e){kn===null?kn=[e]:kn.push(e)}function Bh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,$u(t)):(n.next=i.next,i.next=n),t.interleaved=n,Vt(e,r)}function Vt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ut=!1;function Fu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Uh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function en(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,$&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Vt(e,n)}return i=r.interleaved,i===null?(t.next=t,$u(r)):(t.next=i.next,i.next=t),r.interleaved=t,Vt(e,n)}function vs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Cu(e,n)}}function Nd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Hs(e,t,n,r){var i=e.updateQueue;Ut=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(s!==null){var p=i.baseState;o=0,c=u=l=null,a=s;do{var f=a.lane,y=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(f=t,y=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){p=v.call(y,p,f);break e}p=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(y,p,f):v,f==null)break e;p=ee({},p,f);break e;case 2:Ut=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else y={eventTime:y,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=y,l=p):c=c.next=y,o|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(c===null&&(l=p),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);bn|=o,e.lanes=o,e.memoizedState=p}}function jd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(P(191,i));i.call(r)}}}var bi={},mt=dn(bi),vi=dn(bi),xi=dn(bi);function En(e){if(e===bi)throw Error(P(174));return e}function Iu(e,t){switch(W(xi,t),W(vi,e),W(mt,bi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Qa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Qa(t,e)}G(mt),W(mt,t)}function gr(){G(mt),G(vi),G(xi)}function Hh(e){En(xi.current);var t=En(mt.current),n=Qa(t,e.type);t!==n&&(W(vi,e),W(mt,n))}function zu(e){vi.current===e&&(G(mt),G(vi))}var q=dn(0);function Ws(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var aa=[];function Bu(){for(var e=0;e<aa.length;e++)aa[e]._workInProgressVersionPrimary=null;aa.length=0}var xs=Ot.ReactCurrentDispatcher,la=Ot.ReactCurrentBatchConfig,Rn=0,J=null,ae=null,ce=null,Ks=!1,Jr=!1,wi=0,zv=0;function ge(){throw Error(P(321))}function Uu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lt(e[n],t[n]))return!1;return!0}function Hu(e,t,n,r,i,s){if(Rn=s,J=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,xs.current=e===null||e.memoizedState===null?Wv:Kv,e=n(r,i),Jr){s=0;do{if(Jr=!1,wi=0,25<=s)throw Error(P(301));s+=1,ce=ae=null,t.updateQueue=null,xs.current=Gv,e=n(r,i)}while(Jr)}if(xs.current=Gs,t=ae!==null&&ae.next!==null,Rn=0,ce=ae=J=null,Ks=!1,t)throw Error(P(300));return e}function Wu(){var e=wi!==0;return wi=0,e}function ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ce===null?J.memoizedState=ce=e:ce=ce.next=e,ce}function Ze(){if(ae===null){var e=J.alternate;e=e!==null?e.memoizedState:null}else e=ae.next;var t=ce===null?J.memoizedState:ce.next;if(t!==null)ce=t,ae=e;else{if(e===null)throw Error(P(310));ae=e,e={memoizedState:ae.memoizedState,baseState:ae.baseState,baseQueue:ae.baseQueue,queue:ae.queue,next:null},ce===null?J.memoizedState=ce=e:ce=ce.next=e}return ce}function Si(e,t){return typeof t=="function"?t(e):t}function ua(e){var t=Ze(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=ae,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var c=u.lane;if((Rn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=p,o=r):l=l.next=p,J.lanes|=c,bn|=c}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,lt(r,t.memoizedState)||(Ae=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,J.lanes|=s,bn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ca(e){var t=Ze(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);lt(s,t.memoizedState)||(Ae=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Wh(){}function Kh(e,t){var n=J,r=Ze(),i=t(),s=!lt(r.memoizedState,i);if(s&&(r.memoizedState=i,Ae=!0),r=r.queue,Ku(Qh.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ce!==null&&ce.memoizedState.tag&1){if(n.flags|=2048,ki(9,Yh.bind(null,n,r,i,t),void 0,null),de===null)throw Error(P(349));Rn&30||Gh(n,t,i)}return i}function Gh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=J.updateQueue,t===null?(t={lastEffect:null,stores:null},J.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Yh(e,t,n,r){t.value=n,t.getSnapshot=r,Xh(t)&&qh(e)}function Qh(e,t,n){return n(function(){Xh(t)&&qh(e)})}function Xh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lt(e,n)}catch{return!0}}function qh(e){var t=Vt(e,1);t!==null&&at(t,e,1,-1)}function Md(e){var t=ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Si,lastRenderedState:e},t.queue=e,e=e.dispatch=Hv.bind(null,J,e),[t.memoizedState,e]}function ki(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=J.updateQueue,t===null?(t={lastEffect:null,stores:null},J.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Zh(){return Ze().memoizedState}function ws(e,t,n,r){var i=ft();J.flags|=e,i.memoizedState=ki(1|t,n,void 0,r===void 0?null:r)}function So(e,t,n,r){var i=Ze();r=r===void 0?null:r;var s=void 0;if(ae!==null){var o=ae.memoizedState;if(s=o.destroy,r!==null&&Uu(r,o.deps)){i.memoizedState=ki(t,n,s,r);return}}J.flags|=e,i.memoizedState=ki(1|t,n,s,r)}function Ad(e,t){return ws(8390656,8,e,t)}function Ku(e,t){return So(2048,8,e,t)}function Jh(e,t){return So(4,2,e,t)}function em(e,t){return So(4,4,e,t)}function tm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function nm(e,t,n){return n=n!=null?n.concat([e]):null,So(4,4,tm.bind(null,t,e),n)}function Gu(){}function rm(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Uu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function im(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Uu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function sm(e,t,n){return Rn&21?(lt(n,t)||(n=ch(),J.lanes|=n,bn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ae=!0),e.memoizedState=n)}function Bv(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=la.transition;la.transition={};try{e(!1),t()}finally{I=n,la.transition=r}}function om(){return Ze().memoizedState}function Uv(e,t,n){var r=nn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},am(e))lm(t,n);else if(n=Bh(e,t,n,r),n!==null){var i=Te();at(n,e,r,i),um(n,t,r)}}function Hv(e,t,n){var r=nn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(am(e))lm(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,lt(a,o)){var l=t.interleaved;l===null?(i.next=i,$u(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Bh(e,t,i,r),n!==null&&(i=Te(),at(n,e,r,i),um(n,t,r))}}function am(e){var t=e.alternate;return e===J||t!==null&&t===J}function lm(e,t){Jr=Ks=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function um(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Cu(e,n)}}var Gs={readContext:qe,useCallback:ge,useContext:ge,useEffect:ge,useImperativeHandle:ge,useInsertionEffect:ge,useLayoutEffect:ge,useMemo:ge,useReducer:ge,useRef:ge,useState:ge,useDebugValue:ge,useDeferredValue:ge,useTransition:ge,useMutableSource:ge,useSyncExternalStore:ge,useId:ge,unstable_isNewReconciler:!1},Wv={readContext:qe,useCallback:function(e,t){return ft().memoizedState=[e,t===void 0?null:t],e},useContext:qe,useEffect:Ad,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ws(4194308,4,tm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ws(4194308,4,e,t)},useInsertionEffect:function(e,t){return ws(4,2,e,t)},useMemo:function(e,t){var n=ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Uv.bind(null,J,e),[r.memoizedState,e]},useRef:function(e){var t=ft();return e={current:e},t.memoizedState=e},useState:Md,useDebugValue:Gu,useDeferredValue:function(e){return ft().memoizedState=e},useTransition:function(){var e=Md(!1),t=e[0];return e=Bv.bind(null,e[1]),ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=J,i=ft();if(Q){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),de===null)throw Error(P(349));Rn&30||Gh(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Ad(Qh.bind(null,r,s,e),[e]),r.flags|=2048,ki(9,Yh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=ft(),t=de.identifierPrefix;if(Q){var n=Ct,r=Et;n=(r&~(1<<32-ot(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=wi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=zv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Kv={readContext:qe,useCallback:rm,useContext:qe,useEffect:Ku,useImperativeHandle:nm,useInsertionEffect:Jh,useLayoutEffect:em,useMemo:im,useReducer:ua,useRef:Zh,useState:function(){return ua(Si)},useDebugValue:Gu,useDeferredValue:function(e){var t=Ze();return sm(t,ae.memoizedState,e)},useTransition:function(){var e=ua(Si)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Wh,useSyncExternalStore:Kh,useId:om,unstable_isNewReconciler:!1},Gv={readContext:qe,useCallback:rm,useContext:qe,useEffect:Ku,useImperativeHandle:nm,useInsertionEffect:Jh,useLayoutEffect:em,useMemo:im,useReducer:ca,useRef:Zh,useState:function(){return ca(Si)},useDebugValue:Gu,useDeferredValue:function(e){var t=Ze();return ae===null?t.memoizedState=e:sm(t,ae.memoizedState,e)},useTransition:function(){var e=ca(Si)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Wh,useSyncExternalStore:Kh,useId:om,unstable_isNewReconciler:!1};function rt(e,t){if(e&&e.defaultProps){t=ee({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function gl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ee({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ko={isMounted:function(e){return(e=e._reactInternals)?On(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Te(),i=nn(e),s=Tt(r,i);s.payload=t,n!=null&&(s.callback=n),t=en(e,s,i),t!==null&&(at(t,e,i,r),vs(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Te(),i=nn(e),s=Tt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=en(e,s,i),t!==null&&(at(t,e,i,r),vs(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Te(),r=nn(e),i=Tt(n,r);i.tag=2,t!=null&&(i.callback=t),t=en(e,i,r),t!==null&&(at(t,e,r,n),vs(t,e,r))}};function Ld(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!hi(n,r)||!hi(i,s):!0}function cm(e,t,n){var r=!1,i=on,s=t.contextType;return typeof s=="object"&&s!==null?s=qe(s):(i=De(t)?Ln:Ee.current,r=t.contextTypes,s=(r=r!=null)?pr(e,i):on),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ko,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Dd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ko.enqueueReplaceState(t,t.state,null)}function yl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Fu(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=qe(s):(s=De(t)?Ln:Ee.current,i.context=pr(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(gl(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&ko.enqueueReplaceState(i,i.state,null),Hs(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function yr(e,t){try{var n="",r=t;do n+=Sy(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function da(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function vl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Yv=typeof WeakMap=="function"?WeakMap:Map;function dm(e,t,n){n=Tt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Qs||(Qs=!0,jl=r),vl(e,t)},n}function fm(e,t,n){n=Tt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){vl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){vl(e,t),typeof r!="function"&&(tn===null?tn=new Set([this]):tn.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Rd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Yv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=l1.bind(null,e,t,n),t.then(e,e))}function bd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Vd(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Tt(-1,1),t.tag=2,en(n,t,1))),n.lanes|=1),e)}var Qv=Ot.ReactCurrentOwner,Ae=!1;function Pe(e,t,n,r){t.child=e===null?zh(t,null,n,r):mr(t,e.child,n,r)}function _d(e,t,n,r,i){n=n.render;var s=t.ref;return cr(t,i),r=Hu(e,t,n,r,s,i),n=Wu(),e!==null&&!Ae?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,_t(e,t,i)):(Q&&n&&Du(t),t.flags|=1,Pe(e,t,r,i),t.child)}function Od(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!tc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,pm(e,t,s,r,i)):(e=Cs(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:hi,n(o,r)&&e.ref===t.ref)return _t(e,t,i)}return t.flags|=1,e=rn(s,r),e.ref=t.ref,e.return=t,t.child=e}function pm(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(hi(s,r)&&e.ref===t.ref)if(Ae=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ae=!0);else return t.lanes=e.lanes,_t(e,t,i)}return xl(e,t,n,r,i)}function hm(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(er,Ve),Ve|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(er,Ve),Ve|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,W(er,Ve),Ve|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,W(er,Ve),Ve|=r;return Pe(e,t,i,n),t.child}function mm(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function xl(e,t,n,r,i){var s=De(n)?Ln:Ee.current;return s=pr(t,s),cr(t,i),n=Hu(e,t,n,r,s,i),r=Wu(),e!==null&&!Ae?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,_t(e,t,i)):(Q&&r&&Du(t),t.flags|=1,Pe(e,t,n,i),t.child)}function $d(e,t,n,r,i){if(De(n)){var s=!0;Fs(t)}else s=!1;if(cr(t,i),t.stateNode===null)Ss(e,t),cm(t,n,r),yl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=qe(u):(u=De(n)?Ln:Ee.current,u=pr(t,u));var c=n.getDerivedStateFromProps,p=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";p||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Dd(t,o,r,u),Ut=!1;var f=t.memoizedState;o.state=f,Hs(t,r,o,i),l=t.memoizedState,a!==r||f!==l||Le.current||Ut?(typeof c=="function"&&(gl(t,n,c,r),l=t.memoizedState),(a=Ut||Ld(t,n,a,r,f,l,u))?(p||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Uh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:rt(t.type,a),o.props=u,p=t.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=qe(l):(l=De(n)?Ln:Ee.current,l=pr(t,l));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==p||f!==l)&&Dd(t,o,r,l),Ut=!1,f=t.memoizedState,o.state=f,Hs(t,r,o,i);var v=t.memoizedState;a!==p||f!==v||Le.current||Ut?(typeof y=="function"&&(gl(t,n,y,r),v=t.memoizedState),(u=Ut||Ld(t,n,u,r,f,v,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return wl(e,t,n,r,s,i)}function wl(e,t,n,r,i,s){mm(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Ed(t,n,!1),_t(e,t,s);r=t.stateNode,Qv.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=mr(t,e.child,null,s),t.child=mr(t,null,a,s)):Pe(e,t,a,s),t.memoizedState=r.state,i&&Ed(t,n,!0),t.child}function gm(e){var t=e.stateNode;t.pendingContext?kd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&kd(e,t.context,!1),Iu(e,t.containerInfo)}function Fd(e,t,n,r,i){return hr(),bu(i),t.flags|=256,Pe(e,t,n,r),t.child}var Sl={dehydrated:null,treeContext:null,retryLane:0};function kl(e){return{baseLanes:e,cachePool:null,transitions:null}}function ym(e,t,n){var r=t.pendingProps,i=q.current,s=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),W(q,i&1),e===null)return hl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Po(o,r,0,null),e=Nn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=kl(n),t.memoizedState=Sl,e):Yu(t,o));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Xv(e,t,o,r,a,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=rn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=rn(a,s):(s=Nn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?kl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Sl,r}return s=e.child,e=s.sibling,r=rn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Yu(e,t){return t=Po({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ns(e,t,n,r){return r!==null&&bu(r),mr(t,e.child,null,n),e=Yu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Xv(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=da(Error(P(422))),ns(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Po({mode:"visible",children:r.children},i,0,null),s=Nn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&mr(t,e.child,null,o),t.child.memoizedState=kl(o),t.memoizedState=Sl,s);if(!(t.mode&1))return ns(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(P(419)),r=da(s,r,void 0),ns(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ae||a){if(r=de,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Vt(e,i),at(r,e,i,-1))}return ec(),r=da(Error(P(421))),ns(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=u1.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Oe=Jt(i.nextSibling),$e=t,Q=!0,st=null,e!==null&&(Ge[Ye++]=Et,Ge[Ye++]=Ct,Ge[Ye++]=Dn,Et=e.id,Ct=e.overflow,Dn=t),t=Yu(t,r.children),t.flags|=4096,t)}function Id(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ml(e.return,t,n)}function fa(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function vm(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(Pe(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Id(e,n,t);else if(e.tag===19)Id(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Ws(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),fa(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Ws(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}fa(t,!0,n,null,s);break;case"together":fa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ss(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function _t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),bn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=rn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=rn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qv(e,t,n){switch(t.tag){case 3:gm(t),hr();break;case 5:Hh(t);break;case 1:De(t.type)&&Fs(t);break;case 4:Iu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;W(Bs,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?ym(e,t,n):(W(q,q.current&1),e=_t(e,t,n),e!==null?e.sibling:null);W(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return vm(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,hm(e,t,n)}return _t(e,t,n)}var xm,El,wm,Sm;xm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};El=function(){};wm=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,En(mt.current);var s=null;switch(n){case"input":i=Wa(e,i),r=Wa(e,r),s=[];break;case"select":i=ee({},i,{value:void 0}),r=ee({},r,{value:void 0}),s=[];break;case"textarea":i=Ya(e,i),r=Ya(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Os)}Xa(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ai.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ai.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&K("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Sm=function(e,t,n,r){n!==r&&(t.flags|=4)};function _r(e,t){if(!Q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Zv(e,t,n){var r=t.pendingProps;switch(Ru(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ye(t),null;case 1:return De(t.type)&&$s(),ye(t),null;case 3:return r=t.stateNode,gr(),G(Le),G(Ee),Bu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(es(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,st!==null&&(Ll(st),st=null))),El(e,t),ye(t),null;case 5:zu(t);var i=En(xi.current);if(n=t.type,e!==null&&t.stateNode!=null)wm(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return ye(t),null}if(e=En(mt.current),es(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[pt]=t,r[yi]=s,e=(t.mode&1)!==0,n){case"dialog":K("cancel",r),K("close",r);break;case"iframe":case"object":case"embed":K("load",r);break;case"video":case"audio":for(i=0;i<Hr.length;i++)K(Hr[i],r);break;case"source":K("error",r);break;case"img":case"image":case"link":K("error",r),K("load",r);break;case"details":K("toggle",r);break;case"input":Qc(r,s),K("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},K("invalid",r);break;case"textarea":qc(r,s),K("invalid",r)}Xa(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Ji(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Ji(r.textContent,a,e),i=["children",""+a]):ai.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&K("scroll",r)}switch(n){case"input":Wi(r),Xc(r,s,!0);break;case"textarea":Wi(r),Zc(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Os)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Qp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[pt]=t,e[yi]=r,xm(e,t,!1,!1),t.stateNode=e;e:{switch(o=qa(n,r),n){case"dialog":K("cancel",e),K("close",e),i=r;break;case"iframe":case"object":case"embed":K("load",e),i=r;break;case"video":case"audio":for(i=0;i<Hr.length;i++)K(Hr[i],e);i=r;break;case"source":K("error",e),i=r;break;case"img":case"image":case"link":K("error",e),K("load",e),i=r;break;case"details":K("toggle",e),i=r;break;case"input":Qc(e,r),i=Wa(e,r),K("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ee({},r,{value:void 0}),K("invalid",e);break;case"textarea":qc(e,r),i=Ya(e,r),K("invalid",e);break;default:i=r}Xa(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?Zp(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Xp(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&li(e,l):typeof l=="number"&&li(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(ai.hasOwnProperty(s)?l!=null&&s==="onScroll"&&K("scroll",e):l!=null&&vu(e,s,l,o))}switch(n){case"input":Wi(e),Xc(e,r,!1);break;case"textarea":Wi(e),Zc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+sn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?or(e,!!r.multiple,s,!1):r.defaultValue!=null&&or(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Os)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ye(t),null;case 6:if(e&&t.stateNode!=null)Sm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=En(xi.current),En(mt.current),es(t)){if(r=t.stateNode,n=t.memoizedProps,r[pt]=t,(s=r.nodeValue!==n)&&(e=$e,e!==null))switch(e.tag){case 3:Ji(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ji(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[pt]=t,t.stateNode=r}return ye(t),null;case 13:if(G(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Q&&Oe!==null&&t.mode&1&&!(t.flags&128))Fh(),hr(),t.flags|=98560,s=!1;else if(s=es(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(P(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(P(317));s[pt]=t}else hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ye(t),s=!1}else st!==null&&(Ll(st),st=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?le===0&&(le=3):ec())),t.updateQueue!==null&&(t.flags|=4),ye(t),null);case 4:return gr(),El(e,t),e===null&&mi(t.stateNode.containerInfo),ye(t),null;case 10:return Ou(t.type._context),ye(t),null;case 17:return De(t.type)&&$s(),ye(t),null;case 19:if(G(q),s=t.memoizedState,s===null)return ye(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)_r(s,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Ws(e),o!==null){for(t.flags|=128,_r(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(q,q.current&1|2),t.child}e=e.sibling}s.tail!==null&&se()>vr&&(t.flags|=128,r=!0,_r(s,!1),t.lanes=4194304)}else{if(!r)if(e=Ws(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),_r(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!Q)return ye(t),null}else 2*se()-s.renderingStartTime>vr&&n!==1073741824&&(t.flags|=128,r=!0,_r(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=se(),t.sibling=null,n=q.current,W(q,r?n&1|2:n&1),t):(ye(t),null);case 22:case 23:return Ju(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ve&1073741824&&(ye(t),t.subtreeFlags&6&&(t.flags|=8192)):ye(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function Jv(e,t){switch(Ru(t),t.tag){case 1:return De(t.type)&&$s(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return gr(),G(Le),G(Ee),Bu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return zu(t),null;case 13:if(G(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(q),null;case 4:return gr(),null;case 10:return Ou(t.type._context),null;case 22:case 23:return Ju(),null;case 24:return null;default:return null}}var rs=!1,xe=!1,e1=typeof WeakSet=="function"?WeakSet:Set,M=null;function Jn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ne(e,t,r)}else n.current=null}function Cl(e,t,n){try{n()}catch(r){ne(e,t,r)}}var zd=!1;function t1(e,t){if(al=bs,e=Th(),Lu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,p=e,f=null;t:for(;;){for(var y;p!==n||i!==0&&p.nodeType!==3||(a=o+i),p!==s||r!==0&&p.nodeType!==3||(l=o+r),p.nodeType===3&&(o+=p.nodeValue.length),(y=p.firstChild)!==null;)f=p,p=y;for(;;){if(p===e)break t;if(f===n&&++u===i&&(a=o),f===s&&++c===r&&(l=o),(y=p.nextSibling)!==null)break;p=f,f=p.parentNode}p=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ll={focusedElem:e,selectionRange:n},bs=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:rt(t.type,x),S);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(k){ne(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return v=zd,zd=!1,v}function ei(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Cl(t,n,s)}i=i.next}while(i!==r)}}function Eo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Pl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function km(e){var t=e.alternate;t!==null&&(e.alternate=null,km(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[pt],delete t[yi],delete t[dl],delete t[Ov],delete t[$v])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Em(e){return e.tag===5||e.tag===3||e.tag===4}function Bd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Em(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Tl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Os));else if(r!==4&&(e=e.child,e!==null))for(Tl(e,t,n),e=e.sibling;e!==null;)Tl(e,t,n),e=e.sibling}function Nl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Nl(e,t,n),e=e.sibling;e!==null;)Nl(e,t,n),e=e.sibling}var fe=null,it=!1;function Ft(e,t,n){for(n=n.child;n!==null;)Cm(e,t,n),n=n.sibling}function Cm(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(mo,n)}catch{}switch(n.tag){case 5:xe||Jn(n,t);case 6:var r=fe,i=it;fe=null,Ft(e,t,n),fe=r,it=i,fe!==null&&(it?(e=fe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):fe.removeChild(n.stateNode));break;case 18:fe!==null&&(it?(e=fe,n=n.stateNode,e.nodeType===8?sa(e.parentNode,n):e.nodeType===1&&sa(e,n),fi(e)):sa(fe,n.stateNode));break;case 4:r=fe,i=it,fe=n.stateNode.containerInfo,it=!0,Ft(e,t,n),fe=r,it=i;break;case 0:case 11:case 14:case 15:if(!xe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&Cl(n,t,o),i=i.next}while(i!==r)}Ft(e,t,n);break;case 1:if(!xe&&(Jn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ne(n,t,a)}Ft(e,t,n);break;case 21:Ft(e,t,n);break;case 22:n.mode&1?(xe=(r=xe)||n.memoizedState!==null,Ft(e,t,n),xe=r):Ft(e,t,n);break;default:Ft(e,t,n)}}function Ud(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new e1),t.forEach(function(r){var i=c1.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function nt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:fe=a.stateNode,it=!1;break e;case 3:fe=a.stateNode.containerInfo,it=!0;break e;case 4:fe=a.stateNode.containerInfo,it=!0;break e}a=a.return}if(fe===null)throw Error(P(160));Cm(s,o,i),fe=null,it=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ne(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Pm(t,e),t=t.sibling}function Pm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(nt(t,e),dt(e),r&4){try{ei(3,e,e.return),Eo(3,e)}catch(x){ne(e,e.return,x)}try{ei(5,e,e.return)}catch(x){ne(e,e.return,x)}}break;case 1:nt(t,e),dt(e),r&512&&n!==null&&Jn(n,n.return);break;case 5:if(nt(t,e),dt(e),r&512&&n!==null&&Jn(n,n.return),e.flags&32){var i=e.stateNode;try{li(i,"")}catch(x){ne(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Gp(i,s),qa(a,o);var u=qa(a,s);for(o=0;o<l.length;o+=2){var c=l[o],p=l[o+1];c==="style"?Zp(i,p):c==="dangerouslySetInnerHTML"?Xp(i,p):c==="children"?li(i,p):vu(i,c,p,u)}switch(a){case"input":Ka(i,s);break;case"textarea":Yp(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?or(i,!!s.multiple,y,!1):f!==!!s.multiple&&(s.defaultValue!=null?or(i,!!s.multiple,s.defaultValue,!0):or(i,!!s.multiple,s.multiple?[]:"",!1))}i[yi]=s}catch(x){ne(e,e.return,x)}}break;case 6:if(nt(t,e),dt(e),r&4){if(e.stateNode===null)throw Error(P(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){ne(e,e.return,x)}}break;case 3:if(nt(t,e),dt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{fi(t.containerInfo)}catch(x){ne(e,e.return,x)}break;case 4:nt(t,e),dt(e);break;case 13:nt(t,e),dt(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(qu=se())),r&4&&Ud(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(xe=(u=xe)||c,nt(t,e),xe=u):nt(t,e),dt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(p=M=c;M!==null;){switch(f=M,y=f.child,f.tag){case 0:case 11:case 14:case 15:ei(4,f,f.return);break;case 1:Jn(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){ne(r,n,x)}}break;case 5:Jn(f,f.return);break;case 22:if(f.memoizedState!==null){Wd(p);continue}}y!==null?(y.return=f,M=y):Wd(p)}c=c.sibling}e:for(c=null,p=e;;){if(p.tag===5){if(c===null){c=p;try{i=p.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=p.stateNode,l=p.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=qp("display",o))}catch(x){ne(e,e.return,x)}}}else if(p.tag===6){if(c===null)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(x){ne(e,e.return,x)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;c===p&&(c=null),p=p.return}c===p&&(c=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:nt(t,e),dt(e),r&4&&Ud(e);break;case 21:break;default:nt(t,e),dt(e)}}function dt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Em(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(li(i,""),r.flags&=-33);var s=Bd(e);Nl(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Bd(e);Tl(e,a,o);break;default:throw Error(P(161))}}catch(l){ne(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function n1(e,t,n){M=e,Tm(e)}function Tm(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||rs;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||xe;a=rs;var u=xe;if(rs=o,(xe=l)&&!u)for(M=i;M!==null;)o=M,l=o.child,o.tag===22&&o.memoizedState!==null?Kd(i):l!==null?(l.return=o,M=l):Kd(i);for(;s!==null;)M=s,Tm(s),s=s.sibling;M=i,rs=a,xe=u}Hd(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,M=s):Hd(e)}}function Hd(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:xe||Eo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!xe)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:rt(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&jd(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}jd(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var p=c.dehydrated;p!==null&&fi(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}xe||t.flags&512&&Pl(t)}catch(f){ne(t,t.return,f)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Wd(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Kd(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Eo(4,t)}catch(l){ne(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ne(t,i,l)}}var s=t.return;try{Pl(t)}catch(l){ne(t,s,l)}break;case 5:var o=t.return;try{Pl(t)}catch(l){ne(t,o,l)}}}catch(l){ne(t,t.return,l)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var r1=Math.ceil,Ys=Ot.ReactCurrentDispatcher,Qu=Ot.ReactCurrentOwner,Xe=Ot.ReactCurrentBatchConfig,$=0,de=null,oe=null,he=0,Ve=0,er=dn(0),le=0,Ei=null,bn=0,Co=0,Xu=0,ti=null,Me=null,qu=0,vr=1/0,St=null,Qs=!1,jl=null,tn=null,is=!1,Yt=null,Xs=0,ni=0,Ml=null,ks=-1,Es=0;function Te(){return $&6?se():ks!==-1?ks:ks=se()}function nn(e){return e.mode&1?$&2&&he!==0?he&-he:Iv.transition!==null?(Es===0&&(Es=ch()),Es):(e=I,e!==0||(e=window.event,e=e===void 0?16:yh(e.type)),e):1}function at(e,t,n,r){if(50<ni)throw ni=0,Ml=null,Error(P(185));Li(e,n,r),(!($&2)||e!==de)&&(e===de&&(!($&2)&&(Co|=n),le===4&&Wt(e,he)),Re(e,r),n===1&&$===0&&!(t.mode&1)&&(vr=se()+500,wo&&fn()))}function Re(e,t){var n=e.callbackNode;Iy(e,t);var r=Rs(e,e===de?he:0);if(r===0)n!==null&&td(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&td(n),t===1)e.tag===0?Fv(Gd.bind(null,e)):_h(Gd.bind(null,e)),Vv(function(){!($&6)&&fn()}),n=null;else{switch(dh(r)){case 1:n=Eu;break;case 4:n=lh;break;case 16:n=Ds;break;case 536870912:n=uh;break;default:n=Ds}n=bm(n,Nm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Nm(e,t){if(ks=-1,Es=0,$&6)throw Error(P(327));var n=e.callbackNode;if(dr()&&e.callbackNode!==n)return null;var r=Rs(e,e===de?he:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=qs(e,r);else{t=r;var i=$;$|=2;var s=Mm();(de!==e||he!==t)&&(St=null,vr=se()+500,Tn(e,t));do try{o1();break}catch(a){jm(e,a)}while(!0);_u(),Ys.current=s,$=i,oe!==null?t=0:(de=null,he=0,t=le)}if(t!==0){if(t===2&&(i=nl(e),i!==0&&(r=i,t=Al(e,i))),t===1)throw n=Ei,Tn(e,0),Wt(e,r),Re(e,se()),n;if(t===6)Wt(e,r);else{if(i=e.current.alternate,!(r&30)&&!i1(i)&&(t=qs(e,r),t===2&&(s=nl(e),s!==0&&(r=s,t=Al(e,s))),t===1))throw n=Ei,Tn(e,0),Wt(e,r),Re(e,se()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:yn(e,Me,St);break;case 3:if(Wt(e,r),(r&130023424)===r&&(t=qu+500-se(),10<t)){if(Rs(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Te(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=cl(yn.bind(null,e,Me,St),t);break}yn(e,Me,St);break;case 4:if(Wt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-ot(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*r1(r/1960))-r,10<r){e.timeoutHandle=cl(yn.bind(null,e,Me,St),r);break}yn(e,Me,St);break;case 5:yn(e,Me,St);break;default:throw Error(P(329))}}}return Re(e,se()),e.callbackNode===n?Nm.bind(null,e):null}function Al(e,t){var n=ti;return e.current.memoizedState.isDehydrated&&(Tn(e,t).flags|=256),e=qs(e,t),e!==2&&(t=Me,Me=n,t!==null&&Ll(t)),e}function Ll(e){Me===null?Me=e:Me.push.apply(Me,e)}function i1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!lt(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Wt(e,t){for(t&=~Xu,t&=~Co,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function Gd(e){if($&6)throw Error(P(327));dr();var t=Rs(e,0);if(!(t&1))return Re(e,se()),null;var n=qs(e,t);if(e.tag!==0&&n===2){var r=nl(e);r!==0&&(t=r,n=Al(e,r))}if(n===1)throw n=Ei,Tn(e,0),Wt(e,t),Re(e,se()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,yn(e,Me,St),Re(e,se()),null}function Zu(e,t){var n=$;$|=1;try{return e(t)}finally{$=n,$===0&&(vr=se()+500,wo&&fn())}}function Vn(e){Yt!==null&&Yt.tag===0&&!($&6)&&dr();var t=$;$|=1;var n=Xe.transition,r=I;try{if(Xe.transition=null,I=1,e)return e()}finally{I=r,Xe.transition=n,$=t,!($&6)&&fn()}}function Ju(){Ve=er.current,G(er)}function Tn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,bv(n)),oe!==null)for(n=oe.return;n!==null;){var r=n;switch(Ru(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&$s();break;case 3:gr(),G(Le),G(Ee),Bu();break;case 5:zu(r);break;case 4:gr();break;case 13:G(q);break;case 19:G(q);break;case 10:Ou(r.type._context);break;case 22:case 23:Ju()}n=n.return}if(de=e,oe=e=rn(e.current,null),he=Ve=t,le=0,Ei=null,Xu=Co=bn=0,Me=ti=null,kn!==null){for(t=0;t<kn.length;t++)if(n=kn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}kn=null}return e}function jm(e,t){do{var n=oe;try{if(_u(),xs.current=Gs,Ks){for(var r=J.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Ks=!1}if(Rn=0,ce=ae=J=null,Jr=!1,wi=0,Qu.current=null,n===null||n.return===null){le=1,Ei=t,oe=null;break}e:{var s=e,o=n.return,a=n,l=t;if(t=he,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,p=c.tag;if(!(c.mode&1)&&(p===0||p===11||p===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=bd(o);if(y!==null){y.flags&=-257,Vd(y,o,a,s,t),y.mode&1&&Rd(s,u,t),t=y,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){Rd(s,u,t),ec();break e}l=Error(P(426))}}else if(Q&&a.mode&1){var S=bd(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Vd(S,o,a,s,t),bu(yr(l,a));break e}}s=l=yr(l,a),le!==4&&(le=2),ti===null?ti=[s]:ti.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var m=dm(s,l,t);Nd(s,m);break e;case 1:a=l;var h=s.type,g=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(tn===null||!tn.has(g)))){s.flags|=65536,t&=-t,s.lanes|=t;var k=fm(s,a,t);Nd(s,k);break e}}s=s.return}while(s!==null)}Lm(n)}catch(E){t=E,oe===n&&n!==null&&(oe=n=n.return);continue}break}while(!0)}function Mm(){var e=Ys.current;return Ys.current=Gs,e===null?Gs:e}function ec(){(le===0||le===3||le===2)&&(le=4),de===null||!(bn&268435455)&&!(Co&268435455)||Wt(de,he)}function qs(e,t){var n=$;$|=2;var r=Mm();(de!==e||he!==t)&&(St=null,Tn(e,t));do try{s1();break}catch(i){jm(e,i)}while(!0);if(_u(),$=n,Ys.current=r,oe!==null)throw Error(P(261));return de=null,he=0,le}function s1(){for(;oe!==null;)Am(oe)}function o1(){for(;oe!==null&&!Ly();)Am(oe)}function Am(e){var t=Rm(e.alternate,e,Ve);e.memoizedProps=e.pendingProps,t===null?Lm(e):oe=t,Qu.current=null}function Lm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Jv(n,t),n!==null){n.flags&=32767,oe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,oe=null;return}}else if(n=Zv(n,t,Ve),n!==null){oe=n;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);le===0&&(le=5)}function yn(e,t,n){var r=I,i=Xe.transition;try{Xe.transition=null,I=1,a1(e,t,n,r)}finally{Xe.transition=i,I=r}return null}function a1(e,t,n,r){do dr();while(Yt!==null);if($&6)throw Error(P(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(zy(e,s),e===de&&(oe=de=null,he=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||is||(is=!0,bm(Ds,function(){return dr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Xe.transition,Xe.transition=null;var o=I;I=1;var a=$;$|=4,Qu.current=null,t1(e,n),Pm(n,e),Nv(ll),bs=!!al,ll=al=null,e.current=n,n1(n),Dy(),$=a,I=o,Xe.transition=s}else e.current=n;if(is&&(is=!1,Yt=e,Xs=i),s=e.pendingLanes,s===0&&(tn=null),Vy(n.stateNode),Re(e,se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Qs)throw Qs=!1,e=jl,jl=null,e;return Xs&1&&e.tag!==0&&dr(),s=e.pendingLanes,s&1?e===Ml?ni++:(ni=0,Ml=e):ni=0,fn(),null}function dr(){if(Yt!==null){var e=dh(Xs),t=Xe.transition,n=I;try{if(Xe.transition=null,I=16>e?16:e,Yt===null)var r=!1;else{if(e=Yt,Yt=null,Xs=0,$&6)throw Error(P(331));var i=$;for($|=4,M=e.current;M!==null;){var s=M,o=s.child;if(M.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:ei(8,c,s)}var p=c.child;if(p!==null)p.return=c,M=p;else for(;M!==null;){c=M;var f=c.sibling,y=c.return;if(km(c),c===u){M=null;break}if(f!==null){f.return=y,M=f;break}M=y}}}var v=s.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}M=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,M=o;else e:for(;M!==null;){if(s=M,s.flags&2048)switch(s.tag){case 0:case 11:case 15:ei(9,s,s.return)}var m=s.sibling;if(m!==null){m.return=s.return,M=m;break e}M=s.return}}var h=e.current;for(M=h;M!==null;){o=M;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,M=g;else e:for(o=h;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Eo(9,a)}}catch(E){ne(a,a.return,E)}if(a===o){M=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,M=k;break e}M=a.return}}if($=i,fn(),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(mo,e)}catch{}r=!0}return r}finally{I=n,Xe.transition=t}}return!1}function Yd(e,t,n){t=yr(n,t),t=dm(e,t,1),e=en(e,t,1),t=Te(),e!==null&&(Li(e,1,t),Re(e,t))}function ne(e,t,n){if(e.tag===3)Yd(e,e,n);else for(;t!==null;){if(t.tag===3){Yd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(tn===null||!tn.has(r))){e=yr(n,e),e=fm(t,e,1),t=en(t,e,1),e=Te(),t!==null&&(Li(t,1,e),Re(t,e));break}}t=t.return}}function l1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Te(),e.pingedLanes|=e.suspendedLanes&n,de===e&&(he&n)===n&&(le===4||le===3&&(he&130023424)===he&&500>se()-qu?Tn(e,0):Xu|=n),Re(e,t)}function Dm(e,t){t===0&&(e.mode&1?(t=Yi,Yi<<=1,!(Yi&130023424)&&(Yi=4194304)):t=1);var n=Te();e=Vt(e,t),e!==null&&(Li(e,t,n),Re(e,n))}function u1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Dm(e,n)}function c1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Dm(e,n)}var Rm;Rm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Le.current)Ae=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ae=!1,qv(e,t,n);Ae=!!(e.flags&131072)}else Ae=!1,Q&&t.flags&1048576&&Oh(t,zs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ss(e,t),e=t.pendingProps;var i=pr(t,Ee.current);cr(t,n),i=Hu(null,t,r,e,i,n);var s=Wu();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,De(r)?(s=!0,Fs(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Fu(t),i.updater=ko,t.stateNode=i,i._reactInternals=t,yl(t,r,e,n),t=wl(null,t,r,!0,s,n)):(t.tag=0,Q&&s&&Du(t),Pe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ss(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=f1(r),e=rt(r,e),i){case 0:t=xl(null,t,r,e,n);break e;case 1:t=$d(null,t,r,e,n);break e;case 11:t=_d(null,t,r,e,n);break e;case 14:t=Od(null,t,r,rt(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),xl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),$d(e,t,r,i,n);case 3:e:{if(gm(t),e===null)throw Error(P(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Uh(e,t),Hs(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=yr(Error(P(423)),t),t=Fd(e,t,r,n,i);break e}else if(r!==i){i=yr(Error(P(424)),t),t=Fd(e,t,r,n,i);break e}else for(Oe=Jt(t.stateNode.containerInfo.firstChild),$e=t,Q=!0,st=null,n=zh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(hr(),r===i){t=_t(e,t,n);break e}Pe(e,t,r,n)}t=t.child}return t;case 5:return Hh(t),e===null&&hl(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,ul(r,i)?o=null:s!==null&&ul(r,s)&&(t.flags|=32),mm(e,t),Pe(e,t,o,n),t.child;case 6:return e===null&&hl(t),null;case 13:return ym(e,t,n);case 4:return Iu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=mr(t,null,r,n):Pe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),_d(e,t,r,i,n);case 7:return Pe(e,t,t.pendingProps,n),t.child;case 8:return Pe(e,t,t.pendingProps.children,n),t.child;case 12:return Pe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,W(Bs,r._currentValue),r._currentValue=o,s!==null)if(lt(s.value,o)){if(s.children===i.children&&!Le.current){t=_t(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Tt(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),ml(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(P(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),ml(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Pe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,cr(t,n),i=qe(i),r=r(i),t.flags|=1,Pe(e,t,r,n),t.child;case 14:return r=t.type,i=rt(r,t.pendingProps),i=rt(r.type,i),Od(e,t,r,i,n);case 15:return pm(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Ss(e,t),t.tag=1,De(r)?(e=!0,Fs(t)):e=!1,cr(t,n),cm(t,r,i),yl(t,r,i,n),wl(null,t,r,!0,e,n);case 19:return vm(e,t,n);case 22:return hm(e,t,n)}throw Error(P(156,t.tag))};function bm(e,t){return ah(e,t)}function d1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qe(e,t,n,r){return new d1(e,t,n,r)}function tc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function f1(e){if(typeof e=="function")return tc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===wu)return 11;if(e===Su)return 14}return 2}function rn(e,t){var n=e.alternate;return n===null?(n=Qe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Cs(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")tc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Hn:return Nn(n.children,i,s,t);case xu:o=8,i|=8;break;case za:return e=Qe(12,n,t,i|2),e.elementType=za,e.lanes=s,e;case Ba:return e=Qe(13,n,t,i),e.elementType=Ba,e.lanes=s,e;case Ua:return e=Qe(19,n,t,i),e.elementType=Ua,e.lanes=s,e;case Hp:return Po(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Bp:o=10;break e;case Up:o=9;break e;case wu:o=11;break e;case Su:o=14;break e;case Bt:o=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=Qe(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function Nn(e,t,n,r){return e=Qe(7,e,r,t),e.lanes=n,e}function Po(e,t,n,r){return e=Qe(22,e,r,t),e.elementType=Hp,e.lanes=n,e.stateNode={isHidden:!1},e}function pa(e,t,n){return e=Qe(6,e,null,t),e.lanes=n,e}function ha(e,t,n){return t=Qe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function p1(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Yo(0),this.expirationTimes=Yo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yo(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function nc(e,t,n,r,i,s,o,a,l){return e=new p1(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Qe(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fu(s),e}function h1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Un,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Vm(e){if(!e)return on;e=e._reactInternals;e:{if(On(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(De(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(De(n))return Vh(e,n,t)}return t}function _m(e,t,n,r,i,s,o,a,l){return e=nc(n,r,!0,e,i,s,o,a,l),e.context=Vm(null),n=e.current,r=Te(),i=nn(n),s=Tt(r,i),s.callback=t??null,en(n,s,i),e.current.lanes=i,Li(e,i,r),Re(e,r),e}function To(e,t,n,r){var i=t.current,s=Te(),o=nn(i);return n=Vm(n),t.context===null?t.context=n:t.pendingContext=n,t=Tt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=en(i,t,o),e!==null&&(at(e,i,o,s),vs(e,i,o)),o}function Zs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Qd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function rc(e,t){Qd(e,t),(e=e.alternate)&&Qd(e,t)}function m1(){return null}var Om=typeof reportError=="function"?reportError:function(e){console.error(e)};function ic(e){this._internalRoot=e}No.prototype.render=ic.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));To(e,t,null,null)};No.prototype.unmount=ic.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Vn(function(){To(null,e,null,null)}),t[bt]=null}};function No(e){this._internalRoot=e}No.prototype.unstable_scheduleHydration=function(e){if(e){var t=hh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ht.length&&t!==0&&t<Ht[n].priority;n++);Ht.splice(n,0,e),n===0&&gh(e)}};function sc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function jo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Xd(){}function g1(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=Zs(o);s.call(u)}}var o=_m(t,r,e,0,null,!1,!1,"",Xd);return e._reactRootContainer=o,e[bt]=o.current,mi(e.nodeType===8?e.parentNode:e),Vn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=Zs(l);a.call(u)}}var l=nc(e,0,!1,null,null,!1,!1,"",Xd);return e._reactRootContainer=l,e[bt]=l.current,mi(e.nodeType===8?e.parentNode:e),Vn(function(){To(t,l,n,r)}),l}function Mo(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=Zs(o);a.call(l)}}To(t,o,e,i)}else o=g1(n,t,e,i,r);return Zs(o)}fh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ur(t.pendingLanes);n!==0&&(Cu(t,n|1),Re(t,se()),!($&6)&&(vr=se()+500,fn()))}break;case 13:Vn(function(){var r=Vt(e,1);if(r!==null){var i=Te();at(r,e,1,i)}}),rc(e,1)}};Pu=function(e){if(e.tag===13){var t=Vt(e,134217728);if(t!==null){var n=Te();at(t,e,134217728,n)}rc(e,134217728)}};ph=function(e){if(e.tag===13){var t=nn(e),n=Vt(e,t);if(n!==null){var r=Te();at(n,e,t,r)}rc(e,t)}};hh=function(){return I};mh=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};Ja=function(e,t,n){switch(t){case"input":if(Ka(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=xo(r);if(!i)throw Error(P(90));Kp(r),Ka(r,i)}}}break;case"textarea":Yp(e,n);break;case"select":t=n.value,t!=null&&or(e,!!n.multiple,t,!1)}};th=Zu;nh=Vn;var y1={usingClientEntryPoint:!1,Events:[Ri,Yn,xo,Jp,eh,Zu]},Or={findFiberByHostInstance:Sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},v1={bundleType:Or.bundleType,version:Or.version,rendererPackageName:Or.rendererPackageName,rendererConfig:Or.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ot.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=sh(e),e===null?null:e.stateNode},findFiberByHostInstance:Or.findFiberByHostInstance||m1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ss=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ss.isDisabled&&ss.supportsFiber)try{mo=ss.inject(v1),ht=ss}catch{}}ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=y1;ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!sc(t))throw Error(P(200));return h1(e,t,null,n)};ze.createRoot=function(e,t){if(!sc(e))throw Error(P(299));var n=!1,r="",i=Om;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=nc(e,1,!1,null,null,n,!1,r,i),e[bt]=t.current,mi(e.nodeType===8?e.parentNode:e),new ic(t)};ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=sh(t),e=e===null?null:e.stateNode,e};ze.flushSync=function(e){return Vn(e)};ze.hydrate=function(e,t,n){if(!jo(t))throw Error(P(200));return Mo(null,e,t,!0,n)};ze.hydrateRoot=function(e,t,n){if(!sc(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=Om;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=_m(t,null,e,1,n??null,i,!1,s,o),e[bt]=t.current,mi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new No(t)};ze.render=function(e,t,n){if(!jo(t))throw Error(P(200));return Mo(null,e,t,!1,n)};ze.unmountComponentAtNode=function(e){if(!jo(e))throw Error(P(40));return e._reactRootContainer?(Vn(function(){Mo(null,null,e,!1,function(){e._reactRootContainer=null,e[bt]=null})}),!0):!1};ze.unstable_batchedUpdates=Zu;ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!jo(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return Mo(e,t,n,!1,r)};ze.version="18.3.1-next-f1338f8080-20240426";function $m(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE($m)}catch(e){console.error(e)}}$m(),$p.exports=ze;var Fm=$p.exports,Im,qd=Fm;Im=qd.createRoot,qd.hydrateRoot;/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var x1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),V=(e,t)=>{const n=w.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:a="",children:l,...u},c)=>w.createElement("svg",{ref:c,...x1,width:i,height:i,stroke:r,strokeWidth:o?Number(s)*24/Number(i):s,className:["lucide",`lucide-${w1(e)}`,a].join(" "),...u},[...t.map(([p,f])=>w.createElement(p,f)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zm=V("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bm=V("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jn=V("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S1=V("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ci=V("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k1=V("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zd=V("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jd=V("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=V("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=V("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Um=V("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E1=V("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hm=V("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ef=V("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ma=V("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C1=V("Code2",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bl=V("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P1=V("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T1=V("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=V("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N1=V("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j1=V("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=V("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Js=V("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M1=V("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A1=V("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=V("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=V("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=V("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L1=V("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D1=V("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=V("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tf=V("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ga=V("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R1=V("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b1=V("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=V("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V1=V("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wm=V("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _1=V("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ao=V("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Km=V("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),nf=[{name:"About",icon:jn,href:"#about"},{name:"Skills",icon:Ci,href:"#skills"},{name:"Experience",icon:Il,href:"#experience"},{name:"Metrics",icon:S1,href:"#metrics"},{name:"Challenges",icon:zm,href:"#challenges"},{name:"Patents",icon:jn,href:"#patents"},{name:"Education",icon:jn,href:"#education"}];function O1({className:e=""}){const[t,n]=w.useState(!1),[r,i]=w.useState(!1);return w.useEffect(()=>{const s=()=>{n(window.scrollY>32)};return window.addEventListener("scroll",s),()=>window.removeEventListener("scroll",s)},[]),d.jsx("header",{className:`fixed w-full z-40 transition-all duration-300 ${t?"glass-effect shadow-lg":"bg-transparent"} ${e}`,children:d.jsxs("nav",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs("div",{className:"flex items-center justify-between h-16",children:[d.jsx("a",{href:"#top",className:"text-xl sm:text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors",children:"SG"}),d.jsxs("div",{className:"hidden md:flex items-center gap-6 lg:gap-8",children:[nf.map(s=>d.jsxs("a",{href:s.href,className:"flex items-center gap-2 text-sm lg:text-base text-gray-600 hover:text-blue-600 transition-colors font-medium",children:[d.jsx(s.icon,{className:"w-4 h-4"}),s.name]},s.name)),d.jsxs("div",{className:"flex items-center gap-3 lg:gap-4 ml-4",children:[d.jsx("a",{href:"https://github.com/surenganne",className:"p-2 text-gray-600 hover:text-blue-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:d.jsx(_l,{className:"w-5 h-5"})}),d.jsx("a",{href:"https://www.linkedin.com/in/surenganne/",className:"p-2 text-gray-600 hover:text-blue-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:d.jsx(Ol,{className:"w-5 h-5"})}),d.jsx("a",{href:"mailto:<EMAIL>",className:"p-2 text-gray-600 hover:text-blue-600 transition-colors",children:d.jsx(eo,{className:"w-5 h-5"})})]})]}),d.jsx("button",{className:"md:hidden p-2",onClick:()=>i(!r),children:r?d.jsx(Km,{className:"w-6 h-6"}):d.jsx(L1,{className:"w-6 h-6"})})]}),r&&d.jsxs("div",{className:"md:hidden py-4 space-y-2",children:[nf.map(s=>d.jsxs("a",{href:s.href,className:"flex items-center gap-2 py-2 text-gray-600 hover:text-blue-600 transition-colors font-medium",onClick:()=>i(!1),children:[d.jsx(s.icon,{className:"w-4 h-4"}),s.name]},s.name)),d.jsxs("div",{className:"flex items-center gap-4 pt-4 border-t border-gray-100",children:[d.jsx("a",{href:"https://github.com/surenganne",className:"p-2 text-gray-600 hover:text-blue-600 transition-colors",target:"_blank",rel:"noopener noreferrer",onClick:()=>i(!1),children:d.jsx(_l,{className:"w-5 h-5"})}),d.jsx("a",{href:"https://www.linkedin.com/in/surenganne/",className:"p-2 text-gray-600 hover:text-blue-600 transition-colors",target:"_blank",rel:"noopener noreferrer",onClick:()=>i(!1),children:d.jsx(Ol,{className:"w-5 h-5"})}),d.jsx("a",{href:"mailto:<EMAIL>",className:"p-2 text-gray-600 hover:text-blue-600 transition-colors",onClick:()=>i(!1),children:d.jsx(eo,{className:"w-5 h-5"})})]})]})]})})}function $1(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}function Lo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const zl=e=>Array.isArray(e);function Gm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Pi(e){return typeof e=="string"||Array.isArray(e)}function rf(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function oc(e,t,n,r){if(typeof t=="function"){const[i,s]=rf(r);t=t(n!==void 0?n:e.custom,i,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,s]=rf(r);t=t(n!==void 0?n:e.custom,i,s)}return t}function Do(e,t,n){const r=e.getProps();return oc(r,t,n!==void 0?n:r.custom,e)}const ac=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],lc=["initial",...ac],Vi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],$n=new Set(Vi),Nt=e=>e*1e3,jt=e=>e/1e3,F1={type:"spring",stiffness:500,damping:25,restSpeed:10},I1=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),z1={type:"keyframes",duration:.8},B1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},U1=(e,{keyframes:t})=>t.length>2?z1:$n.has(e)?e.startsWith("scale")?I1(t[1]):F1:B1;function uc(e,t){return e?e[t]||e.default||e:void 0}const H1={skipAnimations:!1,useManualTiming:!1},W1=e=>e!==null;function Ro(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(W1),s=t&&n!=="loop"&&t%2===1?0:i.length-1;return!s||r===void 0?i[s]:r}const ke=e=>e;function K1(e){let t=new Set,n=new Set,r=!1,i=!1;const s=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(u){s.has(u)&&(l.schedule(u),e()),u(o)}const l={schedule:(u,c=!1,p=!1)=>{const y=p&&r?t:n;return c&&s.add(u),y.has(u)||y.add(u),u},cancel:u=>{n.delete(u),s.delete(u)},process:u=>{if(o=u,r){i=!0;return}r=!0,[t,n]=[n,t],n.clear(),t.forEach(a),r=!1,i&&(i=!1,l.process(u))}};return l}const os=["read","resolveKeyframes","update","preRender","render","postRender"],G1=40;function Ym(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,o=os.reduce((m,h)=>(m[h]=K1(s),m),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:p,postRender:f}=o,y=()=>{const m=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(m-i.timestamp,G1),1),i.timestamp=m,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),p.process(i),f.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},v=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:os.reduce((m,h)=>{const g=o[h];return m[h]=(k,E=!1,T=!1)=>(n||v(),g.schedule(k,E,T)),m},{}),cancel:m=>{for(let h=0;h<os.length;h++)o[os[h]].cancel(m)},state:i,steps:o}}const{schedule:z,cancel:an,state:pe,steps:ya}=Ym(typeof requestAnimationFrame<"u"?requestAnimationFrame:ke,!0),Qm=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Y1=1e-7,Q1=12;function X1(e,t,n,r,i){let s,o,a=0;do o=t+(n-t)/2,s=Qm(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>Y1&&++a<Q1);return o}function _i(e,t,n,r){if(e===t&&n===r)return ke;const i=s=>X1(s,0,1,e,n);return s=>s===0||s===1?s:Qm(i(s),t,r)}const Xm=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,qm=e=>t=>1-e(1-t),Zm=_i(.33,1.53,.69,.99),cc=qm(Zm),Jm=Xm(cc),eg=e=>(e*=2)<1?.5*cc(e):.5*(2-Math.pow(2,-10*(e-1))),dc=e=>1-Math.sin(Math.acos(e)),tg=qm(dc),ng=Xm(dc),rg=e=>/^0[^.\s]+$/u.test(e);function q1(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||rg(e):!0}let Bl=ke;const ig=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),sg=e=>t=>typeof t=="string"&&t.startsWith(e),og=sg("--"),Z1=sg("var(--"),fc=e=>Z1(e)?J1.test(e.split("/*")[0].trim()):!1,J1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ex=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function tx(e){const t=ex.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function ag(e,t,n=1){const[r,i]=tx(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return ig(o)?parseFloat(o):o}return fc(i)?ag(i,t,n+1):i}const ln=(e,t,n)=>n>t?t:n<e?e:n,Tr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Ti={...Tr,transform:e=>ln(0,1,e)},as={...Tr,default:1},Oi=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),zt=Oi("deg"),gt=Oi("%"),L=Oi("px"),nx=Oi("vh"),rx=Oi("vw"),sf={...gt,parse:e=>gt.parse(e)/100,transform:e=>gt.transform(e*100)},ix=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),of=e=>e===Tr||e===L,af=(e,t)=>parseFloat(e.split(", ")[t]),lf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return af(i[1],t);{const s=r.match(/^matrix\((.+)\)$/u);return s?af(s[1],e):0}},sx=new Set(["x","y","z"]),ox=Vi.filter(e=>!sx.has(e));function ax(e){const t=[];return ox.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const xr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:lf(4,13),y:lf(5,14)};xr.translateX=xr.x;xr.translateY=xr.y;const lg=e=>t=>t.test(e),lx={test:e=>e==="auto",parse:e=>e},ug=[Tr,L,gt,zt,rx,nx,lx],uf=e=>ug.find(lg(e)),Mn=new Set;let Ul=!1,Hl=!1;function cg(){if(Hl){const e=Array.from(Mn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=ax(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([s,o])=>{var a;(a=r.getValue(s))===null||a===void 0||a.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Hl=!1,Ul=!1,Mn.forEach(e=>e.complete()),Mn.clear()}function dg(){Mn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Hl=!0)})}function ux(){dg(),cg()}class pc{constructor(t,n,r,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Mn.add(this),Ul||(Ul=!0,z.read(dg),z.resolveKeyframes(cg))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let s=0;s<t.length;s++)if(t[s]===null)if(s===0){const o=i==null?void 0:i.get(),a=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),i&&o===void 0&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Mn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Mn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const ri=e=>Math.round(e*1e5)/1e5,hc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function cx(e){return e==null}const dx=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,mc=(e,t)=>n=>!!(typeof n=="string"&&dx.test(n)&&n.startsWith(e)||t&&!cx(n)&&Object.prototype.hasOwnProperty.call(n,t)),fg=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,s,o,a]=r.match(hc);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},fx=e=>ln(0,255,e),va={...Tr,transform:e=>Math.round(fx(e))},Cn={test:mc("rgb","red"),parse:fg("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+va.transform(e)+", "+va.transform(t)+", "+va.transform(n)+", "+ri(Ti.transform(r))+")"};function px(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Wl={test:mc("#"),parse:px,transform:Cn.transform},tr={test:mc("hsl","hue"),parse:fg("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+gt.transform(ri(t))+", "+gt.transform(ri(n))+", "+ri(Ti.transform(r))+")"},ve={test:e=>Cn.test(e)||Wl.test(e)||tr.test(e),parse:e=>Cn.test(e)?Cn.parse(e):tr.test(e)?tr.parse(e):Wl.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Cn.transform(e):tr.transform(e)},hx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function mx(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(hc))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(hx))===null||n===void 0?void 0:n.length)||0)>0}const pg="number",hg="color",gx="var",yx="var(",cf="${}",vx=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ni(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let s=0;const a=t.replace(vx,l=>(ve.test(l)?(r.color.push(s),i.push(hg),n.push(ve.parse(l))):l.startsWith(yx)?(r.var.push(s),i.push(gx),n.push(l)):(r.number.push(s),i.push(pg),n.push(parseFloat(l))),++s,cf)).split(cf);return{values:n,split:a,indexes:r,types:i}}function mg(e){return Ni(e).values}function gg(e){const{split:t,types:n}=Ni(e),r=t.length;return i=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],i[o]!==void 0){const a=n[o];a===pg?s+=ri(i[o]):a===hg?s+=ve.transform(i[o]):s+=i[o]}return s}}const xx=e=>typeof e=="number"?0:e;function wx(e){const t=mg(e);return gg(e)(t.map(xx))}const un={test:mx,parse:mg,createTransformer:gg,getAnimatableNone:wx},Sx=new Set(["brightness","contrast","saturate","opacity"]);function kx(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(hc)||[];if(!r)return e;const i=n.replace(r,"");let s=Sx.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const Ex=/\b([a-z-]*)\(.*?\)/gu,Kl={...un,getAnimatableNone:e=>{const t=e.match(Ex);return t?t.map(kx).join(" "):e}},Cx={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,backgroundPositionX:L,backgroundPositionY:L},Px={rotate:zt,rotateX:zt,rotateY:zt,rotateZ:zt,scale:as,scaleX:as,scaleY:as,scaleZ:as,skew:zt,skewX:zt,skewY:zt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:Ti,originX:sf,originY:sf,originZ:L},df={...Tr,transform:Math.round},gc={...Cx,...Px,zIndex:df,size:L,fillOpacity:Ti,strokeOpacity:Ti,numOctaves:df},Tx={...gc,color:ve,backgroundColor:ve,outlineColor:ve,fill:ve,stroke:ve,borderColor:ve,borderTopColor:ve,borderRightColor:ve,borderBottomColor:ve,borderLeftColor:ve,filter:Kl,WebkitFilter:Kl},yc=e=>Tx[e];function yg(e,t){let n=yc(e);return n!==Kl&&(n=un),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Nx=new Set(["auto","none","0"]);function jx(e,t,n){let r=0,i;for(;r<e.length&&!i;){const s=e[r];typeof s=="string"&&!Nx.has(s)&&Ni(s).values.length&&(i=e[r]),r++}if(i&&n)for(const s of t)e[s]=yg(n,i)}class vg extends pc{constructor(t,n,r,i,s){super(t,n,r,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),fc(u))){const c=ag(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!ix.has(r)||t.length!==2)return;const[i,s]=t,o=uf(i),a=uf(s);if(o!==a)if(of(o)&&of(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)q1(t[i])&&r.push(i);r.length&&jx(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=xr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const s=n.getValue(r);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,a=i[o];i[o]=xr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function vc(e){return typeof e=="function"}let Ps;function Mx(){Ps=void 0}const yt={now:()=>(Ps===void 0&&yt.set(pe.isProcessing||H1.useManualTiming?pe.timestamp:performance.now()),Ps),set:e=>{Ps=e,queueMicrotask(Mx)}},ff=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(un.test(e)||e==="0")&&!e.startsWith("url("));function Ax(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Lx(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],o=ff(i,t),a=ff(s,t);return!o||!a?!1:Ax(e)||(n==="spring"||vc(n))&&r}const Dx=40;class xg{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=yt.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:s,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Dx?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&ux(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=yt.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:s,delay:o,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!Lx(t,r,i,s))if(o)this.options.duration=0;else{l==null||l(Ro(t,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function wg(e,t){return t?e*(1e3/t):0}const Rx=5;function Sg(e,t,n){const r=Math.max(t-Rx,0);return wg(n-e(r),t-r)}const xa=.001,bx=.01,Vx=10,_x=.05,Ox=1;function $x({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,s,o=1-t;o=ln(_x,Ox,o),e=ln(bx,Vx,jt(e)),o<1?(i=u=>{const c=u*o,p=c*e,f=c-n,y=Gl(u,o),v=Math.exp(-p);return xa-f/y*v},s=u=>{const p=u*o*e,f=p*n+n,y=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-p),x=Gl(Math.pow(u,2),o);return(-i(u)+xa>0?-1:1)*((f-y)*v)/x}):(i=u=>{const c=Math.exp(-u*e),p=(u-n)*e+1;return-xa+c*p},s=u=>{const c=Math.exp(-u*e),p=(n-u)*(e*e);return c*p});const a=5/e,l=Ix(i,s,a);if(e=Nt(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const Fx=12;function Ix(e,t,n){let r=n;for(let i=1;i<Fx;i++)r=r-e(r)/t(r);return r}function Gl(e,t){return e*Math.sqrt(1-t*t)}const zx=["duration","bounce"],Bx=["stiffness","damping","mass"];function pf(e,t){return t.some(n=>e[n]!==void 0)}function Ux(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!pf(e,Bx)&&pf(e,zx)){const n=$x(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function kg({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],s=e[e.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:p,isResolvedFromDuration:f}=Ux({...r,velocity:-jt(r.velocity||0)}),y=p||0,v=l/(2*Math.sqrt(a*u)),x=s-i,S=jt(Math.sqrt(a/u)),m=Math.abs(x)<5;n||(n=m?.01:2),t||(t=m?.005:.5);let h;if(v<1){const g=Gl(S,v);h=k=>{const E=Math.exp(-v*S*k);return s-E*((y+v*S*x)/g*Math.sin(g*k)+x*Math.cos(g*k))}}else if(v===1)h=g=>s-Math.exp(-S*g)*(x+(y+S*x)*g);else{const g=S*Math.sqrt(v*v-1);h=k=>{const E=Math.exp(-v*S*k),T=Math.min(g*k,300);return s-E*((y+v*S*x)*Math.sinh(T)+g*x*Math.cosh(T))/g}}return{calculatedDuration:f&&c||null,next:g=>{const k=h(g);if(f)o.done=g>=c;else{let E=0;v<1&&(E=g===0?Nt(y):Sg(h,g,k));const T=Math.abs(E)<=n,j=Math.abs(s-k)<=t;o.done=T&&j}return o.value=o.done?s:k,o}}}function hf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const p=e[0],f={done:!1,value:p},y=C=>a!==void 0&&C<a||l!==void 0&&C>l,v=C=>a===void 0?l:l===void 0||Math.abs(a-C)<Math.abs(l-C)?a:l;let x=n*t;const S=p+x,m=o===void 0?S:o(S);m!==S&&(x=m-p);const h=C=>-x*Math.exp(-C/r),g=C=>m+h(C),k=C=>{const b=h(C),A=g(C);f.done=Math.abs(b)<=u,f.value=f.done?m:A};let E,T;const j=C=>{y(f.value)&&(E=C,T=kg({keyframes:[f.value,v(f.value)],velocity:Sg(g,C,f.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:C=>{let b=!1;return!T&&E===void 0&&(b=!0,k(C),j(C)),E!==void 0&&C>=E?T.next(C-E):(!b&&k(C),f)}}}const Hx=_i(.42,0,1,1),Wx=_i(0,0,.58,1),Eg=_i(.42,0,.58,1),Kx=e=>Array.isArray(e)&&typeof e[0]!="number",xc=e=>Array.isArray(e)&&typeof e[0]=="number",mf={linear:ke,easeIn:Hx,easeInOut:Eg,easeOut:Wx,circIn:dc,circInOut:ng,circOut:tg,backIn:cc,backInOut:Jm,backOut:Zm,anticipate:eg},gf=e=>{if(xc(e)){Bl(e.length===4);const[t,n,r,i]=e;return _i(t,n,r,i)}else if(typeof e=="string")return Bl(mf[e]!==void 0),mf[e];return e},Gx=(e,t)=>n=>t(e(n)),Mt=(...e)=>e.reduce(Gx),wr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Z=(e,t,n)=>e+(t-e)*n;function wa(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Yx({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=wa(l,a,e+1/3),s=wa(l,a,e),o=wa(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}function to(e,t){return n=>n>0?t:e}const Sa=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},Qx=[Wl,Cn,tr],Xx=e=>Qx.find(t=>t.test(e));function yf(e){const t=Xx(e);if(!t)return!1;let n=t.parse(e);return t===tr&&(n=Yx(n)),n}const vf=(e,t)=>{const n=yf(e),r=yf(t);if(!n||!r)return to(e,t);const i={...n};return s=>(i.red=Sa(n.red,r.red,s),i.green=Sa(n.green,r.green,s),i.blue=Sa(n.blue,r.blue,s),i.alpha=Z(n.alpha,r.alpha,s),Cn.transform(i))},Yl=new Set(["none","hidden"]);function qx(e,t){return Yl.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Zx(e,t){return n=>Z(e,t,n)}function wc(e){return typeof e=="number"?Zx:typeof e=="string"?fc(e)?to:ve.test(e)?vf:tw:Array.isArray(e)?Cg:typeof e=="object"?ve.test(e)?vf:Jx:to}function Cg(e,t){const n=[...e],r=n.length,i=e.map((s,o)=>wc(s)(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}}function Jx(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=wc(e[i])(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}}function ew(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],a=e.indexes[o][i[o]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[s]=l,i[o]++}return r}const tw=(e,t)=>{const n=un.createTransformer(t),r=Ni(e),i=Ni(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Yl.has(e)&&!i.values.length||Yl.has(t)&&!r.values.length?qx(e,t):Mt(Cg(ew(r,i),i.values),n):to(e,t)};function Pg(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?Z(e,t,n):wc(e)(e,t)}function nw(e,t,n){const r=[],i=n||Pg,s=e.length-1;for(let o=0;o<s;o++){let a=i(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||ke:t;a=Mt(l,a)}r.push(a)}return r}function rw(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(Bl(s===t.length),s===1)return()=>t[0];if(s===2&&e[0]===e[1])return()=>t[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=nw(t,r,i),a=o.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const p=wr(e[c],e[c+1],u);return o[c](p)};return n?u=>l(ln(e[0],e[s-1],u)):l}function iw(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=wr(0,t,r);e.push(Z(n,1,i))}}function sw(e){const t=[0];return iw(t,e.length-1),t}function ow(e,t){return e.map(n=>n*t)}function aw(e,t){return e.map(()=>t||Eg).splice(0,e.length-1)}function no({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Kx(r)?r.map(gf):gf(r),s={done:!1,value:t[0]},o=ow(n&&n.length===t.length?n:sw(t),e),a=rw(o,t,{ease:Array.isArray(i)?i:aw(t,i)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}const xf=2e4;function lw(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<xf;)t+=n,r=e.next(t);return t>=xf?1/0:t}const uw=e=>{const t=({timestamp:n})=>e(n);return{start:()=>z.update(t,!0),stop:()=>an(t),now:()=>pe.isProcessing?pe.timestamp:yt.now()}},cw={decay:hf,inertia:hf,tween:no,keyframes:no,spring:kg},dw=e=>e/100;class Sc extends xg{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:i,keyframes:s}=this.options,o=(i==null?void 0:i.KeyframeResolver)||pc,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new o(s,a,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,a=vc(n)?n:cw[n]||no;let l,u;a!==no&&typeof t[0]!="number"&&(l=Mt(dw,Pg(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});s==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=lw(c));const{calculatedDuration:p}=c,f=p+i,y=f*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:p,resolvedDuration:f,totalDuration:y}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:p}=r;if(this.startTime===null)return s.next(0);const{delay:f,repeat:y,repeatType:v,repeatDelay:x,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const m=this.currentTime-f*(this.speed>=0?1:-1),h=this.speed>=0?m<0:m>c;this.currentTime=Math.max(m,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let g=this.currentTime,k=s;if(y){const C=Math.min(this.currentTime,c)/p;let b=Math.floor(C),A=C%1;!A&&C>=1&&(A=1),A===1&&b--,b=Math.min(b,y+1),!!(b%2)&&(v==="reverse"?(A=1-A,x&&(A-=x/p)):v==="mirror"&&(k=o)),g=ln(0,1,A)*p}const E=h?{done:!1,value:l[0]}:k.next(g);a&&(E.value=a(E.value));let{done:T}=E;!h&&u!==null&&(T=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const j=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&T);return j&&i!==void 0&&(E.value=Ro(l,this.options,i)),S&&S(E.value),j&&this.finish(),E}get duration(){const{resolved:t}=this;return t?jt(t.calculatedDuration):0}get time(){return jt(this.currentTime)}set time(t){t=Nt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=jt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=uw,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const fw=new Set(["opacity","clipPath","filter","transform"]),pw=10,hw=(e,t)=>{let n="";const r=Math.max(Math.round(t/pw),2);for(let i=0;i<r;i++)n+=e(wr(0,r-1,i))+", ";return`linear(${n.substring(0,n.length-2)})`};function kc(e){let t;return()=>(t===void 0&&(t=e()),t)}const mw={linearEasing:void 0};function gw(e,t){const n=kc(e);return()=>{var r;return(r=mw[t])!==null&&r!==void 0?r:n()}}const ro=gw(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function Tg(e){return!!(typeof e=="function"&&ro()||!e||typeof e=="string"&&(e in Ql||ro())||xc(e)||Array.isArray(e)&&e.every(Tg))}const Wr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Ql={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Wr([0,.65,.55,1]),circOut:Wr([.55,0,1,.45]),backIn:Wr([.31,.01,.66,-.59]),backOut:Wr([.33,1.53,.69,.99])};function Ng(e,t){if(e)return typeof e=="function"&&ro()?hw(e,t):xc(e)?Wr(e):Array.isArray(e)?e.map(n=>Ng(n,t)||Ql.easeOut):Ql[e]}function yw(e,t,n,{delay:r=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=Ng(a,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}function wf(e,t){e.timeline=t,e.onfinish=null}const vw=kc(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),io=10,xw=2e4;function ww(e){return vc(e.type)||e.type==="spring"||!Tg(e.ease)}function Sw(e,t){const n=new Sc({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let s=0;for(;!r.done&&s<xw;)r=n.sample(s),i.push(r.value),s+=io;return{times:void 0,keyframes:i,duration:s-io,ease:"linear"}}const jg={anticipate:eg,backInOut:Jm,circInOut:ng};function kw(e){return e in jg}class Sf extends xg{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:s}=this.options;this.resolver=new vg(s,(o,a)=>this.onKeyframesResolved(o,a),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){var r;let{duration:i=300,times:s,ease:o,type:a,motionValue:l,name:u,startTime:c}=this.options;if(!(!((r=l.owner)===null||r===void 0)&&r.current))return!1;if(typeof o=="string"&&ro()&&kw(o)&&(o=jg[o]),ww(this.options)){const{onComplete:f,onUpdate:y,motionValue:v,element:x,...S}=this.options,m=Sw(t,S);t=m.keyframes,t.length===1&&(t[1]=t[0]),i=m.duration,s=m.times,o=m.ease,a="keyframes"}const p=yw(l.owner.current,u,t,{...this.options,duration:i,times:s,ease:o});return p.startTime=c??this.calcStartTime(),this.pendingTimeline?(wf(p,this.pendingTimeline),this.pendingTimeline=void 0):p.onfinish=()=>{const{onComplete:f}=this.options;l.set(Ro(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:p,duration:i,times:s,type:a,ease:o,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return jt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return jt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Nt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return ke;const{animation:r}=n;wf(r,t)}return ke}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:s,ease:o,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:p,element:f,...y}=this.options,v=new Sc({...y,keyframes:r,duration:i,type:s,ease:o,times:a,isGenerator:!0}),x=Nt(this.time);u.setWithVelocity(v.sample(x-io).value,v.sample(x).value,io)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:s,damping:o,type:a}=t;return vw()&&r&&fw.has(r)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!i&&s!=="mirror"&&o!==0&&a!=="inertia"}}const Ew=kc(()=>window.ScrollTimeline!==void 0);class Cw{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,n){return Promise.all(this.animations).then(t).catch(n)}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>Ew()&&i.attachTimeline?i.attachTimeline(t):n(i));return()=>{r.forEach((i,s)=>{i&&i(),this.animations[s].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function Pw({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Ec=(e,t,n,r={},i,s)=>o=>{const a=uc(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Nt(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:i};Pw(a)||(c={...c,...U1(e,c)}),c.duration&&(c.duration=Nt(c.duration)),c.repeatDelay&&(c.repeatDelay=Nt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let p=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(p=!0)),p&&!s&&t.get()!==void 0){const f=Ro(c.keyframes,a);if(f!==void 0)return z.update(()=>{c.onUpdate(f),c.onComplete()}),new Cw([])}return!s&&Sf.supports(c)?new Sf(c):new Sc(c)},Tw=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Nw=e=>zl(e)?e[e.length-1]||0:e;function Cc(e,t){e.indexOf(t)===-1&&e.push(t)}function Pc(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Tc{constructor(){this.subscriptions=[]}add(t){return Cc(this.subscriptions,t),()=>Pc(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const kf=30,jw=e=>!isNaN(parseFloat(e));class Mw{constructor(t,n={}){this.version="11.11.17",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const s=yt.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=yt.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=jw(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Tc);const r=this.events[t].add(n);return t==="change"?()=>{r(),z.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=yt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>kf)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,kf);return wg(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ji(e,t){return new Mw(e,t)}function Aw(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ji(n))}function Lw(e,t){const n=Do(e,t);let{transitionEnd:r={},transition:i={},...s}=n||{};s={...s,...r};for(const o in s){const a=Nw(s[o]);Aw(e,o,a)}}const Nc=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Dw="framerAppearId",Mg="data-"+Nc(Dw);function Ag(e){return e.props[Mg]}const we=e=>!!(e&&e.getVelocity);function Rw(e){return!!(we(e)&&e.add)}function Xl(e,t){const n=e.getValue("willChange");if(Rw(n))return n.add(t)}function bw({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Lg(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var s;let{transition:o=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const p in l){const f=e.getValue(p,(s=e.latestValues[p])!==null&&s!==void 0?s:null),y=l[p];if(y===void 0||c&&bw(c,p))continue;const v={delay:n,...uc(o||{},p)};let x=!1;if(window.MotionHandoffAnimation){const m=Ag(e);if(m){const h=window.MotionHandoffAnimation(m,p,z);h!==null&&(v.startTime=h,x=!0)}}Xl(e,p),f.start(Ec(p,f,y,e.shouldReduceMotion&&$n.has(p)?{type:!1}:v,e,x));const S=f.animation;S&&u.push(S)}return a&&Promise.all(u).then(()=>{z.update(()=>{a&&Lw(e,a)})}),u}function ql(e,t,n={}){var r;const i=Do(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Lg(e,i,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:p,staggerDirection:f}=s;return Vw(e,t,c+u,p,f,n)}:()=>Promise.resolve(),{when:l}=s;if(l){const[u,c]=l==="beforeChildren"?[o,a]:[a,o];return u().then(()=>c())}else return Promise.all([o(),a(n.delay)])}function Vw(e,t,n=0,r=0,i=1,s){const o=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(_w).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(ql(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function _w(e,t){return e.sortNodePosition(t)}function Ow(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>ql(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=ql(e,t,n);else{const i=typeof t=="function"?Do(e,t,n.custom):t;r=Promise.all(Lg(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const $w=lc.length;function Dg(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Dg(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<$w;n++){const r=lc[n],i=e.props[r];(Pi(i)||i===!1)&&(t[r]=i)}return t}const Fw=[...ac].reverse(),Iw=ac.length;function zw(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Ow(e,n,r)))}function Bw(e){let t=zw(e),n=Ef(),r=!0;const i=l=>(u,c)=>{var p;const f=Do(e,c,l==="exit"?(p=e.presenceContext)===null||p===void 0?void 0:p.custom:void 0);if(f){const{transition:y,transitionEnd:v,...x}=f;u={...u,...x,...v}}return u};function s(l){t=l(e)}function o(l){const{props:u}=e,c=Dg(e.parent)||{},p=[],f=new Set;let y={},v=1/0;for(let S=0;S<Iw;S++){const m=Fw[S],h=n[m],g=u[m]!==void 0?u[m]:c[m],k=Pi(g),E=m===l?h.isActive:null;E===!1&&(v=S);let T=g===c[m]&&g!==u[m]&&k;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),h.protectedKeys={...y},!h.isActive&&E===null||!g&&!h.prevProp||Lo(g)||typeof g=="boolean")continue;const j=Uw(h.prevProp,g);let C=j||m===l&&h.isActive&&!T&&k||S>v&&k,b=!1;const A=Array.isArray(g)?g:[g];let Y=A.reduce(i(m),{});E===!1&&(Y={});const{prevResolvedValues:Ce={}}=h,be={...Ce,...Y},tt=B=>{C=!0,f.has(B)&&(b=!0,f.delete(B)),h.needsAnimating[B]=!0;const N=e.getValue(B);N&&(N.liveStyle=!1)};for(const B in be){const N=Y[B],D=Ce[B];if(y.hasOwnProperty(B))continue;let R=!1;zl(N)&&zl(D)?R=!Gm(N,D):R=N!==D,R?N!=null?tt(B):f.add(B):N!==void 0&&f.has(B)?tt(B):h.protectedKeys[B]=!0}h.prevProp=g,h.prevResolvedValues=Y,h.isActive&&(y={...y,...Y}),r&&e.blockInitialAnimation&&(C=!1),C&&(!(T&&j)||b)&&p.push(...A.map(B=>({animation:B,options:{type:m}})))}if(f.size){const S={};f.forEach(m=>{const h=e.getBaseTarget(m),g=e.getValue(m);g&&(g.liveStyle=!0),S[m]=h??null}),p.push({animation:S})}let x=!!p.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(p):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(f=>{var y;return(y=f.animationState)===null||y===void 0?void 0:y.setActive(l,u)}),n[l].isActive=u;const p=o(l);for(const f in n)n[f].protectedKeys={};return p}return{animateChanges:o,setActive:a,setAnimateFunction:s,getState:()=>n,reset:()=>{n=Ef(),r=!0}}}function Uw(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Gm(t,e):!1}function mn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ef(){return{animate:mn(!0),whileInView:mn(),whileHover:mn(),whileTap:mn(),whileDrag:mn(),whileFocus:mn(),exit:mn()}}class pn{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Hw extends pn{constructor(t){super(t),t.animationState||(t.animationState=Bw(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Lo(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let Ww=0;class Kw extends pn{constructor(){super(...arguments),this.id=Ww++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Gw={animation:{Feature:Hw},exit:{Feature:Kw}},Rg=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function bo(e,t="page"){return{point:{x:e[`${t}X`],y:e[`${t}Y`]}}}const Yw=e=>t=>Rg(t)&&e(t,bo(t));function Pt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function At(e,t,n,r){return Pt(e,t,Yw(n),r)}const Cf=(e,t)=>Math.abs(e-t);function Qw(e,t){const n=Cf(e.x,t.x),r=Cf(e.y,t.y);return Math.sqrt(n**2+r**2)}class bg{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const p=Ea(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,y=Qw(p.offset,{x:0,y:0})>=3;if(!f&&!y)return;const{point:v}=p,{timestamp:x}=pe;this.history.push({...v,timestamp:x});const{onStart:S,onMove:m}=this.handlers;f||(S&&S(this.lastMoveEvent,p),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,p)},this.handlePointerMove=(p,f)=>{this.lastMoveEvent=p,this.lastMoveEventInfo=ka(f,this.transformPagePoint),z.update(this.updatePoint,!0)},this.handlePointerUp=(p,f)=>{this.end();const{onEnd:y,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Ea(p.type==="pointercancel"?this.lastMoveEventInfo:ka(f,this.transformPagePoint),this.history);this.startEvent&&y&&y(p,S),v&&v(p,S)},!Rg(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=bo(t),a=ka(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=pe;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Ea(a,this.history)),this.removeListeners=Mt(At(this.contextWindow,"pointermove",this.handlePointerMove),At(this.contextWindow,"pointerup",this.handlePointerUp),At(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),an(this.updatePoint)}}function ka(e,t){return t?{point:t(e.point)}:e}function Pf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ea({point:e},t){return{point:e,delta:Pf(e,Vg(t)),offset:Pf(e,Xw(t)),velocity:qw(t,.1)}}function Xw(e){return e[0]}function Vg(e){return e[e.length-1]}function qw(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Vg(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Nt(t)));)n--;if(!r)return{x:0,y:0};const s=jt(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function _g(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Tf=_g("dragHorizontal"),Nf=_g("dragVertical");function Og(e){let t=!1;if(e==="y")t=Nf();else if(e==="x")t=Tf();else{const n=Tf(),r=Nf();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function $g(){const e=Og(!0);return e?(e(),!1):!0}function nr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const Fg=1e-4,Zw=1-Fg,Jw=1+Fg,Ig=.01,eS=0-Ig,tS=0+Ig;function Ie(e){return e.max-e.min}function nS(e,t,n){return Math.abs(e-t)<=n}function jf(e,t,n,r=.5){e.origin=r,e.originPoint=Z(t.min,t.max,e.origin),e.scale=Ie(n)/Ie(t),e.translate=Z(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Zw&&e.scale<=Jw||isNaN(e.scale))&&(e.scale=1),(e.translate>=eS&&e.translate<=tS||isNaN(e.translate))&&(e.translate=0)}function ii(e,t,n,r){jf(e.x,t.x,n.x,r?r.originX:void 0),jf(e.y,t.y,n.y,r?r.originY:void 0)}function Mf(e,t,n){e.min=n.min+t.min,e.max=e.min+Ie(t)}function rS(e,t,n){Mf(e.x,t.x,n.x),Mf(e.y,t.y,n.y)}function Af(e,t,n){e.min=t.min-n.min,e.max=e.min+Ie(t)}function si(e,t,n){Af(e.x,t.x,n.x),Af(e.y,t.y,n.y)}function iS(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Z(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Z(n,e,r.max):Math.min(e,n)),e}function Lf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function sS(e,{top:t,left:n,bottom:r,right:i}){return{x:Lf(e.x,n,i),y:Lf(e.y,t,r)}}function Df(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function oS(e,t){return{x:Df(e.x,t.x),y:Df(e.y,t.y)}}function aS(e,t){let n=.5;const r=Ie(e),i=Ie(t);return i>r?n=wr(t.min,t.max-r,e.min):r>i&&(n=wr(e.min,e.max-i,t.min)),ln(0,1,n)}function lS(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Zl=.35;function uS(e=Zl){return e===!1?e=0:e===!0&&(e=Zl),{x:Rf(e,"left","right"),y:Rf(e,"top","bottom")}}function Rf(e,t,n){return{min:bf(e,t),max:bf(e,n)}}function bf(e,t){return typeof e=="number"?e:e[t]||0}const Vf=()=>({translate:0,scale:1,origin:0,originPoint:0}),rr=()=>({x:Vf(),y:Vf()}),_f=()=>({min:0,max:0}),ie=()=>({x:_f(),y:_f()});function Ke(e){return[e("x"),e("y")]}function zg({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function cS({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function dS(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ca(e){return e===void 0||e===1}function Jl({scale:e,scaleX:t,scaleY:n}){return!Ca(e)||!Ca(t)||!Ca(n)}function vn(e){return Jl(e)||Bg(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Bg(e){return Of(e.x)||Of(e.y)}function Of(e){return e&&e!=="0%"}function so(e,t,n){const r=e-n,i=t*r;return n+i}function $f(e,t,n,r,i){return i!==void 0&&(e=so(e,i,r)),so(e,n,r)+t}function eu(e,t=0,n=1,r,i){e.min=$f(e.min,t,n,r,i),e.max=$f(e.max,t,n,r,i)}function Ug(e,{x:t,y:n}){eu(e.x,t.translate,t.scale,t.originPoint),eu(e.y,n.translate,n.scale,n.originPoint)}const Ff=.999999999999,If=1.0000000000001;function fS(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let a=0;a<i;a++){s=n[a],o=s.projectionDelta;const{visualElement:l}=s.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&sr(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Ug(e,o)),r&&vn(s.latestValues)&&sr(e,s.latestValues))}t.x<If&&t.x>Ff&&(t.x=1),t.y<If&&t.y>Ff&&(t.y=1)}function ir(e,t){e.min=e.min+t,e.max=e.max+t}function zf(e,t,n,r,i=.5){const s=Z(e.min,e.max,i);eu(e,t,n,s,r)}function sr(e,t){zf(e.x,t.x,t.scaleX,t.scale,t.originX),zf(e.y,t.y,t.scaleY,t.scale,t.originY)}function Hg(e,t){return zg(dS(e.getBoundingClientRect(),t))}function pS(e,t,n){const r=Hg(e,n),{scroll:i}=t;return i&&(ir(r.x,i.offset.x),ir(r.y,i.offset.y)),r}const Wg=({current:e})=>e?e.ownerDocument.defaultView:null,hS=new WeakMap;class mS{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ie(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:p}=this.getProps();p?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(bo(c,"page").point)},s=(c,p)=>{const{drag:f,dragPropagation:y,onDragStart:v}=this.getProps();if(f&&!y&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Og(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ke(S=>{let m=this.getAxisMotionValue(S).get()||0;if(gt.test(m)){const{projection:h}=this.visualElement;if(h&&h.layout){const g=h.layout.layoutBox[S];g&&(m=Ie(g)*(parseFloat(m)/100))}}this.originPoint[S]=m}),v&&z.postRender(()=>v(c,p)),Xl(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(c,p)=>{const{dragPropagation:f,dragDirectionLock:y,onDirectionLock:v,onDrag:x}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:S}=p;if(y&&this.currentDirection===null){this.currentDirection=gS(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",p.point,S),this.updateAxis("y",p.point,S),this.visualElement.render(),x&&x(c,p)},a=(c,p)=>this.stop(c,p),l=()=>Ke(c=>{var p;return this.getAnimationState(c)==="paused"&&((p=this.getAxisMotionValue(c).animation)===null||p===void 0?void 0:p.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new bg(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Wg(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&z.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!ls(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=iS(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&nr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=sS(i.layoutBox,n):this.constraints=!1,this.elastic=uS(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ke(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=lS(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nr(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=pS(r,i.root,this.visualElement.getTransformPagePoint());let o=oS(i.layout.layoutBox,s);if(n){const a=n(cS(o));this.hasMutatedConstraints=!!a,a&&(o=zg(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Ke(c=>{if(!ls(c,n,this.currentDirection))return;let p=l&&l[c]||{};o&&(p={min:0,max:0});const f=i?200:1e6,y=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:y,timeConstant:750,restDelta:1,restSpeed:10,...s,...p};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Xl(this.visualElement,t),r.start(Ec(t,r,0,n,this.visualElement,!1))}stopAnimation(){Ke(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ke(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ke(n=>{const{drag:r}=this.getProps();if(!ls(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];s.set(t[n]-Z(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!nr(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ke(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();i[o]=aS({min:l,max:l},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ke(o=>{if(!ls(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(Z(l,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;hS.set(this.visualElement,this);const t=this.visualElement.current,n=At(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();nr(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),z.read(r);const o=Pt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ke(c=>{const p=this.getAxisMotionValue(c);p&&(this.originPoint[c]+=l[c].translate,p.set(p.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Zl,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:a}}}function ls(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function gS(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class yS extends pn{constructor(t){super(t),this.removeGroupControls=ke,this.removeListeners=ke,this.controls=new mS(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ke}unmount(){this.removeGroupControls(),this.removeListeners()}}const Bf=e=>(t,n)=>{e&&z.postRender(()=>e(t,n))};class vS extends pn{constructor(){super(...arguments),this.removePointerDownListener=ke}onPointerDown(t){this.session=new bg(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Wg(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Bf(t),onStart:Bf(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&z.postRender(()=>i(s,o))}}}mount(){this.removePointerDownListener=At(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const jc=w.createContext(null);function xS(){const e=w.useContext(jc);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=w.useId();w.useEffect(()=>r(i),[]);const s=w.useCallback(()=>n&&n(i),[i,n]);return!t&&n?[!1,s]:[!0]}const Kg=w.createContext({}),Gg=w.createContext({}),Ts={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Uf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const $r={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=Uf(e,t.target.x),r=Uf(e,t.target.y);return`${n}% ${r}%`}},wS={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=un.parse(e);if(i.length>5)return r;const s=un.createTransformer(e),o=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+o]/=a,i[1+o]/=l;const u=Z(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}},oo={};function SS(e){Object.assign(oo,e)}const{schedule:Mc,cancel:oP}=Ym(queueMicrotask,!1);class kS extends w.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;SS(ES),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Ts.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||z.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Mc.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Yg(e){const[t,n]=xS(),r=w.useContext(Kg);return d.jsx(kS,{...e,layoutGroup:r,switchLayoutGroup:w.useContext(Gg),isPresent:t,safeToRemove:n})}const ES={borderRadius:{...$r,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:$r,borderTopRightRadius:$r,borderBottomLeftRadius:$r,borderBottomRightRadius:$r,boxShadow:wS},Qg=["TopLeft","TopRight","BottomLeft","BottomRight"],CS=Qg.length,Hf=e=>typeof e=="string"?parseFloat(e):e,Wf=e=>typeof e=="number"||L.test(e);function PS(e,t,n,r,i,s){i?(e.opacity=Z(0,n.opacity!==void 0?n.opacity:1,TS(r)),e.opacityExit=Z(t.opacity!==void 0?t.opacity:1,0,NS(r))):s&&(e.opacity=Z(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<CS;o++){const a=`border${Qg[o]}Radius`;let l=Kf(t,a),u=Kf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||Wf(l)===Wf(u)?(e[a]=Math.max(Z(Hf(l),Hf(u),r),0),(gt.test(u)||gt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=Z(t.rotate||0,n.rotate||0,r))}function Kf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const TS=Xg(0,.5,tg),NS=Xg(.5,.95,ke);function Xg(e,t,n){return r=>r<e?0:r>t?1:n(wr(e,t,r))}function Gf(e,t){e.min=t.min,e.max=t.max}function We(e,t){Gf(e.x,t.x),Gf(e.y,t.y)}function Yf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Qf(e,t,n,r,i){return e-=t,e=so(e,1/n,r),i!==void 0&&(e=so(e,1/i,r)),e}function jS(e,t=0,n=1,r=.5,i,s=e,o=e){if(gt.test(t)&&(t=parseFloat(t),t=Z(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=Z(s.min,s.max,r);e===s&&(a-=t),e.min=Qf(e.min,t,n,a,i),e.max=Qf(e.max,t,n,a,i)}function Xf(e,t,[n,r,i],s,o){jS(e,t[n],t[r],t[i],t.scale,s,o)}const MS=["x","scaleX","originX"],AS=["y","scaleY","originY"];function qf(e,t,n,r){Xf(e.x,t,MS,n?n.x:void 0,r?r.x:void 0),Xf(e.y,t,AS,n?n.y:void 0,r?r.y:void 0)}function Zf(e){return e.translate===0&&e.scale===1}function qg(e){return Zf(e.x)&&Zf(e.y)}function Jf(e,t){return e.min===t.min&&e.max===t.max}function LS(e,t){return Jf(e.x,t.x)&&Jf(e.y,t.y)}function ep(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Zg(e,t){return ep(e.x,t.x)&&ep(e.y,t.y)}function tp(e){return Ie(e.x)/Ie(e.y)}function np(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class DS{constructor(){this.members=[]}add(t){Cc(this.members,t),t.scheduleRender()}remove(t){if(Pc(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function RS(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((i||s||o)&&(r=`translate3d(${i}px, ${s}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:p,rotateY:f,skewX:y,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),p&&(r+=`rotateX(${p}deg) `),f&&(r+=`rotateY(${f}deg) `),y&&(r+=`skewX(${y}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const bS=(e,t)=>e.depth-t.depth;class VS{constructor(){this.children=[],this.isDirty=!1}add(t){Cc(this.children,t),this.isDirty=!0}remove(t){Pc(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(bS),this.isDirty=!1,this.children.forEach(t)}}function Ns(e){const t=we(e)?e.get():e;return Tw(t)?t.toValue():t}function _S(e,t){const n=yt.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(an(r),e(s-t))};return z.read(r,!0),()=>an(r)}function OS(e){return e instanceof SVGElement&&e.tagName!=="svg"}function $S(e,t,n){const r=we(e)?e:ji(e);return r.start(Ec("",r,t,n)),r.animation}const xn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Kr=typeof window<"u"&&window.MotionDebug!==void 0,Pa=["","X","Y","Z"],FS={visibility:"hidden"},rp=1e3;let IS=0;function Ta(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Jg(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Ag(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",z,!(i||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Jg(r)}function e0({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},a=t==null?void 0:t()){this.id=IS++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Kr&&(xn.totalNodes=xn.resolvedTargetDeltas=xn.recalculatedProjection=0),this.nodes.forEach(US),this.nodes.forEach(YS),this.nodes.forEach(QS),this.nodes.forEach(HS),Kr&&window.MotionDebug.record(xn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new VS)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Tc),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=OS(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let p;const f=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,p&&p(),p=_S(f,250),Ts.hasAnimatedSinceResize&&(Ts.hasAnimatedSinceResize=!1,this.nodes.forEach(sp))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:p,hasLayoutChanged:f,hasRelativeTargetChanged:y,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||e2,{onLayoutAnimationStart:S,onLayoutAnimationComplete:m}=c.getProps(),h=!this.targetLayout||!Zg(this.targetLayout,v)||y,g=!f&&y;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(p,g);const k={...uc(x,"layout"),onPlay:S,onComplete:m};(c.shouldReduceMotion||this.options.layoutRoot)&&(k.delay=0,k.type=!1),this.startAnimation(k)}else f||sp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,an(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(XS),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Jg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const p=this.path[c];p.shouldResetTransform=!0,p.updateScroll("snapshot"),p.options.layoutRoot&&p.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ip);return}this.isUpdating||this.nodes.forEach(KS),this.isUpdating=!1,this.nodes.forEach(GS),this.nodes.forEach(zS),this.nodes.forEach(BS),this.clearAllSnapshots();const a=yt.now();pe.delta=ln(0,1e3/60,a-pe.timestamp),pe.timestamp=a,pe.isProcessing=!0,ya.update.process(pe),ya.preRender.process(pe),ya.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Mc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(WS),this.sharedNodes.forEach(qS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,z.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){z.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ie(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!qg(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||vn(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),t2(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return ie();const l=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(n2))){const{scroll:c}=this.root;c&&(ir(l.x,c.offset.x),ir(l.y,c.offset.y))}return l}removeElementScroll(o){var a;const l=ie();if(We(l,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:p,options:f}=c;c!==this.root&&p&&f.layoutScroll&&(p.wasRoot&&We(l,o),ir(l.x,p.offset.x),ir(l.y,p.offset.y))}return l}applyTransform(o,a=!1){const l=ie();We(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&sr(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),vn(c.latestValues)&&sr(l,c.latestValues)}return vn(this.latestValues)&&sr(l,this.latestValues),l}removeTransform(o){const a=ie();We(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!vn(u.latestValues))continue;Jl(u.latestValues)&&u.updateSnapshot();const c=ie(),p=u.measurePageBox();We(c,p),qf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return vn(this.latestValues)&&qf(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:p,layoutId:f}=this.options;if(!(!this.layout||!(p||f))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const y=this.getClosestProjectingParent();y&&y.layout&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),si(this.relativeTargetOrigin,this.layout.layoutBox,y.layout.layoutBox),We(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ie(),this.targetWithTransforms=ie()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),rS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):We(this.target,this.layout.layoutBox),Ug(this.target,this.targetDelta)):We(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const y=this.getClosestProjectingParent();y&&!!y.resumingFrom==!!this.resumingFrom&&!y.options.layoutScroll&&y.target&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),si(this.relativeTargetOrigin,this.target,y.target),We(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Kr&&xn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Jl(this.parent.latestValues)||Bg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:p}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||p))return;We(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,y=this.treeScale.y;fS(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=ie());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Yf(this.prevProjectionDelta.x,this.projectionDelta.x),Yf(this.prevProjectionDelta.y,this.projectionDelta.y)),ii(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==y||!np(this.projectionDelta.x,this.prevProjectionDelta.x)||!np(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),Kr&&xn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rr(),this.projectionDelta=rr(),this.projectionDeltaWithTransform=rr()}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},p=rr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=ie(),y=l?l.source:void 0,v=this.layout?this.layout.source:void 0,x=y!==v,S=this.getStack(),m=!S||S.members.length<=1,h=!!(x&&!m&&this.options.crossfade===!0&&!this.path.some(JS));this.animationProgress=0;let g;this.mixTargetDelta=k=>{const E=k/1e3;op(p.x,o.x,E),op(p.y,o.y,E),this.setTargetDelta(p),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(si(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),ZS(this.relativeTarget,this.relativeTargetOrigin,f,E),g&&LS(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=ie()),We(g,this.relativeTarget)),x&&(this.animationValues=c,PS(c,u,this.latestValues,E,h,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=E},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(an(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=z.update(()=>{Ts.hasAnimatedSinceResize=!0,this.currentAnimation=$S(0,rp,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(rp),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&t0(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ie();const p=Ie(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+p;const f=Ie(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}We(a,l),sr(a,c),ii(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new DS),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&Ta("z",o,u,this.animationValues);for(let c=0;c<Pa.length;c++)Ta(`rotate${Pa[c]}`,o,u,this.animationValues),Ta(`skew${Pa[c]}`,o,u,this.animationValues);o.render();for(const c in u)o.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return FS;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ns(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const p=this.getLead();if(!this.projectionDelta||!this.layout||!p.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Ns(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!vn(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const f=p.animationValues||p.latestValues;this.applyTransformsToTarget(),u.transform=RS(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:y,y:v}=this.projectionDelta;u.transformOrigin=`${y.origin*100}% ${v.origin*100}% 0`,p.animationValues?u.opacity=p===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=p===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const x in oo){if(f[x]===void 0)continue;const{correct:S,applyTo:m}=oo[x],h=u.transform==="none"?f[x]:S(f[x],p);if(m){const g=m.length;for(let k=0;k<g;k++)u[m[k]]=h}else u[x]=h}return this.options.layoutId&&(u.pointerEvents=p===this?Ns(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(ip),this.root.sharedNodes.clear()}}}function zS(e){e.updateLayout()}function BS(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?Ke(p=>{const f=o?n.measuredBox[p]:n.layoutBox[p],y=Ie(f);f.min=r[p].min,f.max=f.min+y}):t0(s,n.layoutBox,r)&&Ke(p=>{const f=o?n.measuredBox[p]:n.layoutBox[p],y=Ie(r[p]);f.max=f.min+y,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[p].max=e.relativeTarget[p].min+y)});const a=rr();ii(a,r,n.layoutBox);const l=rr();o?ii(l,e.applyTransform(i,!0),n.measuredBox):ii(l,r,n.layoutBox);const u=!qg(a);let c=!1;if(!e.resumeFrom){const p=e.getClosestProjectingParent();if(p&&!p.resumeFrom){const{snapshot:f,layout:y}=p;if(f&&y){const v=ie();si(v,n.layoutBox,f.layoutBox);const x=ie();si(x,r,y.layoutBox),Zg(v,x)||(c=!0),p.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=p)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function US(e){Kr&&xn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function HS(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function WS(e){e.clearSnapshot()}function ip(e){e.clearMeasurements()}function KS(e){e.isLayoutDirty=!1}function GS(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function sp(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function YS(e){e.resolveTargetDelta()}function QS(e){e.calcProjection()}function XS(e){e.resetSkewAndRotation()}function qS(e){e.removeLeadSnapshot()}function op(e,t,n){e.translate=Z(t.translate,0,n),e.scale=Z(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function ap(e,t,n,r){e.min=Z(t.min,n.min,r),e.max=Z(t.max,n.max,r)}function ZS(e,t,n,r){ap(e.x,t.x,n.x,r),ap(e.y,t.y,n.y,r)}function JS(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const e2={duration:.45,ease:[.4,0,.1,1]},lp=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),up=lp("applewebkit/")&&!lp("chrome/")?Math.round:ke;function cp(e){e.min=up(e.min),e.max=up(e.max)}function t2(e){cp(e.x),cp(e.y)}function t0(e,t,n){return e==="position"||e==="preserve-aspect"&&!nS(tp(t),tp(n),.2)}function n2(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const r2=e0({attachResizeListener:(e,t)=>Pt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Na={current:void 0},n0=e0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Na.current){const e=new r2({});e.mount(window),e.setOptions({layoutScroll:!0}),Na.current=e}return Na.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),i2={pan:{Feature:vS},drag:{Feature:yS,ProjectionNode:n0,MeasureLayout:Yg}};function dp(e,t){const n=t?"pointerenter":"pointerleave",r=t?"onHoverStart":"onHoverEnd",i=(s,o)=>{if(s.pointerType==="touch"||$g())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t);const l=a[r];l&&z.postRender(()=>l(s,o))};return At(e.current,n,i,{passive:!e.getProps()[r]})}class s2 extends pn{mount(){this.unmount=Mt(dp(this.node,!0),dp(this.node,!1))}unmount(){}}class o2 extends pn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Mt(Pt(this.node.current,"focus",()=>this.onFocus()),Pt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const r0=(e,t)=>t?e===t?!0:r0(e,t.parentElement):!1;function ja(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,bo(n))}class a2 extends pn{constructor(){super(...arguments),this.removeStartListeners=ke,this.removeEndListeners=ke,this.removeAccessibleListeners=ke,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),s=At(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:p}=this.node.getProps(),f=!p&&!r0(this.node.current,a.target)?c:u;f&&z.update(()=>f(a,l))},{passive:!(r.onTap||r.onPointerUp)}),o=At(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Mt(s,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=s=>{if(s.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||ja("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&z.postRender(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=Pt(this.node.current,"keyup",o),ja("down",(a,l)=>{this.startPress(a,l)})},n=Pt(this.node.current,"keydown",t),r=()=>{this.isPressing&&ja("cancel",(s,o)=>this.cancelPress(s,o))},i=Pt(this.node.current,"blur",r);this.removeAccessibleListeners=Mt(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&z.postRender(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!$g()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&z.postRender(()=>r(t,n))}mount(){const t=this.node.getProps(),n=At(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Pt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Mt(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const tu=new WeakMap,Ma=new WeakMap,l2=e=>{const t=tu.get(e.target);t&&t(e)},u2=e=>{e.forEach(l2)};function c2({root:e,...t}){const n=e||document;Ma.has(n)||Ma.set(n,{});const r=Ma.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(u2,{root:e,...t})),r[i]}function d2(e,t,n){const r=c2(t);return tu.set(e,n),r.observe(e),()=>{tu.delete(e),r.unobserve(e)}}const f2={some:0,all:1};class p2 extends pn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:f2[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:p}=this.node.getProps(),f=u?c:p;f&&f(l)};return d2(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(h2(t,n))&&this.startObserver()}unmount(){}}function h2({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const m2={inView:{Feature:p2},tap:{Feature:a2},focus:{Feature:o2},hover:{Feature:s2}},g2={layout:{ProjectionNode:n0,MeasureLayout:Yg}},i0=w.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Vo=w.createContext({}),Ac=typeof window<"u",y2=Ac?w.useLayoutEffect:w.useEffect,s0=w.createContext({strict:!1});function v2(e,t,n,r,i){var s,o;const{visualElement:a}=w.useContext(Vo),l=w.useContext(s0),u=w.useContext(jc),c=w.useContext(i0).reducedMotion,p=w.useRef();r=r||l.renderer,!p.current&&r&&(p.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=p.current,y=w.useContext(Gg);f&&!f.projection&&i&&(f.type==="html"||f.type==="svg")&&x2(p.current,n,i,y);const v=w.useRef(!1);w.useInsertionEffect(()=>{f&&v.current&&f.update(n,u)});const x=n[Mg],S=w.useRef(!!x&&!(!((s=window.MotionHandoffIsComplete)===null||s===void 0)&&s.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return y2(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Mc.render(f.render),S.current&&f.animationState&&f.animationState.animateChanges())}),w.useEffect(()=>{f&&(!S.current&&f.animationState&&f.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var m;(m=window.MotionHandoffMarkAsComplete)===null||m===void 0||m.call(window,x)}),S.current=!1))}),f}function x2(e,t,n,r){const{layoutId:i,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:o0(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||a&&nr(a),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function o0(e){if(e)return e.options.allowProjection!==!1?e.projection:o0(e.parent)}function w2(e,t,n){return w.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):nr(n)&&(n.current=r))},[t])}function _o(e){return Lo(e.animate)||lc.some(t=>Pi(e[t]))}function a0(e){return!!(_o(e)||e.variants)}function S2(e,t){if(_o(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Pi(n)?n:void 0,animate:Pi(r)?r:void 0}}return e.inherit!==!1?t:{}}function k2(e){const{initial:t,animate:n}=S2(e,w.useContext(Vo));return w.useMemo(()=>({initial:t,animate:n}),[fp(t),fp(n)])}function fp(e){return Array.isArray(e)?e.join(" "):e}const pp={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Sr={};for(const e in pp)Sr[e]={isEnabled:t=>pp[e].some(n=>!!t[n])};function E2(e){for(const t in e)Sr[t]={...Sr[t],...e[t]}}const C2=Symbol.for("motionComponentSymbol");function P2({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&E2(e);function s(a,l){let u;const c={...w.useContext(i0),...a,layoutId:T2(a)},{isStatic:p}=c,f=k2(a),y=r(a,p);if(!p&&Ac){N2();const v=j2(c);u=v.MeasureLayout,f.visualElement=v2(i,y,c,t,v.ProjectionNode)}return d.jsxs(Vo.Provider,{value:f,children:[u&&f.visualElement?d.jsx(u,{visualElement:f.visualElement,...c}):null,n(i,a,w2(y,f.visualElement,l),y,p,f.visualElement)]})}const o=w.forwardRef(s);return o[C2]=i,o}function T2({layoutId:e}){const t=w.useContext(Kg).id;return t&&e!==void 0?t+"-"+e:e}function N2(e,t){w.useContext(s0).strict}function j2(e){const{drag:t,layout:n}=Sr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const M2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Lc(e){return typeof e!="string"||e.includes("-")?!1:!!(M2.indexOf(e)>-1||/[A-Z]/u.test(e))}function l0(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const u0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function c0(e,t,n,r){l0(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(u0.has(i)?i:Nc(i),t.attrs[i])}function d0(e,{layout:t,layoutId:n}){return $n.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!oo[e]||e==="opacity")}function Dc(e,t,n){var r;const{style:i}=e,s={};for(const o in i)(we(i[o])||t.style&&we(t.style[o])||d0(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(s[o]=i[o]);return s}function f0(e,t,n){const r=Dc(e,t,n);for(const i in e)if(we(e[i])||we(t[i])){const s=Vi.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[s]=e[i]}return r}function A2(e){const t=w.useRef(null);return t.current===null&&(t.current=e()),t.current}function L2({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,s){const o={latestValues:D2(r,i,s,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const p0=e=>(t,n)=>{const r=w.useContext(Vo),i=w.useContext(jc),s=()=>L2(e,t,r,i);return n?s():A2(s)};function D2(e,t,n,r){const i={},s=r(e,{});for(const f in s)i[f]=Ns(s[f]);let{initial:o,animate:a}=e;const l=_o(e),u=a0(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const p=c?a:o;if(p&&typeof p!="boolean"&&!Lo(p)){const f=Array.isArray(p)?p:[p];for(let y=0;y<f.length;y++){const v=oc(e,f[y]);if(v){const{transitionEnd:x,transition:S,...m}=v;for(const h in m){let g=m[h];if(Array.isArray(g)){const k=c?g.length-1:0;g=g[k]}g!==null&&(i[h]=g)}for(const h in x)i[h]=x[h]}}}return i}const Rc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),h0=()=>({...Rc(),attrs:{}}),m0=(e,t)=>t&&typeof e=="number"?t.transform(e):e,R2={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},b2=Vi.length;function V2(e,t,n){let r="",i=!0;for(let s=0;s<b2;s++){const o=Vi[s],a=e[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=m0(a,gc[o]);if(!l){i=!1;const c=R2[o]||o;r+=`${c}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function bc(e,t,n){const{style:r,vars:i,transformOrigin:s}=e;let o=!1,a=!1;for(const l in t){const u=t[l];if($n.has(l)){o=!0;continue}else if(og(l)){i[l]=u;continue}else{const c=m0(u,gc[l]);l.startsWith("origin")?(a=!0,s[l]=c):r[l]=c}}if(t.transform||(o||n?r.transform=V2(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin=`${l} ${u} ${c}`}}function hp(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function _2(e,t,n){const r=hp(t,e.x,e.width),i=hp(n,e.y,e.height);return`${r} ${i}`}const O2={offset:"stroke-dashoffset",array:"stroke-dasharray"},$2={offset:"strokeDashoffset",array:"strokeDasharray"};function F2(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?O2:$2;e[s.offset]=L.transform(-r);const o=L.transform(t),a=L.transform(n);e[s.array]=`${o} ${a}`}function Vc(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,p){if(bc(e,u,p),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:y,dimensions:v}=e;f.transform&&(v&&(y.transform=f.transform),delete f.transform),v&&(i!==void 0||s!==void 0||y.transform)&&(y.transformOrigin=_2(v,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&F2(f,o,a,l,!1)}const _c=e=>typeof e=="string"&&e.toLowerCase()==="svg",I2={useVisualState:p0({scrapeMotionValuesFromProps:f0,createRenderState:h0,onMount:(e,t,{renderState:n,latestValues:r})=>{z.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),z.render(()=>{Vc(n,r,_c(t.tagName),e.transformTemplate),c0(t,n)})}})},z2={useVisualState:p0({scrapeMotionValuesFromProps:Dc,createRenderState:Rc})};function g0(e,t,n){for(const r in t)!we(t[r])&&!d0(r,n)&&(e[r]=t[r])}function B2({transformTemplate:e},t){return w.useMemo(()=>{const n=Rc();return bc(n,t,e),Object.assign({},n.vars,n.style)},[t])}function U2(e,t){const n=e.style||{},r={};return g0(r,n,e),Object.assign(r,B2(e,t)),r}function H2(e,t){const n={},r=U2(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const W2=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ao(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||W2.has(e)}let y0=e=>!ao(e);function K2(e){e&&(y0=t=>t.startsWith("on")?!ao(t):e(t))}try{K2(require("@emotion/is-prop-valid").default)}catch{}function G2(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(y0(i)||n===!0&&ao(i)||!t&&!ao(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Y2(e,t,n,r){const i=w.useMemo(()=>{const s=h0();return Vc(s,t,_c(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};g0(s,e.style,e),i.style={...s,...i.style}}return i}function Q2(e=!1){return(n,r,i,{latestValues:s},o)=>{const l=(Lc(n)?Y2:H2)(r,s,o,n),u=G2(r,typeof n=="string",e),c=n!==w.Fragment?{...u,...l,ref:i}:{},{children:p}=r,f=w.useMemo(()=>we(p)?p.get():p,[p]);return w.createElement(n,{...c,children:f})}}function X2(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const o={...Lc(r)?I2:z2,preloadedFeatures:e,useRender:Q2(i),createVisualElement:t,Component:r};return P2(o)}}const nu={current:null},v0={current:!1};function q2(){if(v0.current=!0,!!Ac)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>nu.current=e.matches;e.addListener(t),t()}else nu.current=!1}function Z2(e,t,n){for(const r in t){const i=t[r],s=n[r];if(we(i))e.addValue(r,i);else if(we(s))e.addValue(r,ji(i,{owner:e}));else if(s!==i)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=e.getStaticValue(r);e.addValue(r,ji(o!==void 0?o:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const mp=new WeakMap,J2=[...ug,ve,un],ek=e=>J2.find(lg(e)),gp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tk{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=pc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=yt.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,z.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=_o(n),this.isVariantNode=a0(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...p}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in p){const y=p[f];l[f]!==void 0&&we(y)&&y.set(l[f],!1)}}mount(t){this.current=t,mp.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),v0.current||q2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:nu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){mp.delete(this.current),this.projection&&this.projection.unmount(),an(this.notifyUpdate),an(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=$n.has(t),i=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&z.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Sr){const n=Sr[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ie()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<gp.length;r++){const i=gp[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s="on"+i,o=t[s];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Z2(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ji(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(ig(i)||rg(i))?i=parseFloat(i):!ek(i)&&un.test(n)&&(i=yg(t,n)),this.setBaseTarget(t,we(i)?i.get():i)),we(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const o=oc(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[t])}if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!we(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Tc),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class x0 extends tk{constructor(){super(...arguments),this.KeyframeResolver=vg}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}}function nk(e){return window.getComputedStyle(e)}class rk extends x0{constructor(){super(...arguments),this.type="html",this.renderInstance=l0}readValueFromInstance(t,n){if($n.has(n)){const r=yc(n);return r&&r.default||0}else{const r=nk(t),i=(og(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Hg(t,n)}build(t,n,r){bc(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Dc(t,n,r)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;we(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}class ik extends x0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ie}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if($n.has(n)){const r=yc(n);return r&&r.default||0}return n=u0.has(n)?n:Nc(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return f0(t,n,r)}build(t,n,r){Vc(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){c0(t,n,r,i)}mount(t){this.isSVGTag=_c(t.tagName),super.mount(t)}}const sk=(e,t)=>Lc(e)?new ik(t):new rk(t,{allowProjection:e!==w.Fragment}),ok=X2({...Gw,...m2,...i2,...g2},sk),re=$1(ok),Aa=[{title:"Product Development",skills:[{name:"Web Applications",icon:Js},{name:"Mobile Apps",icon:tf},{name:"System Architecture",icon:Zd},{name:"API Design",icon:bl},{name:"UI/UX",icon:tf},{name:"End-to-end Testing",icon:b1}]},{title:"Technical Leadership",skills:[{name:"AI/ML Development",icon:Ci},{name:"Cloud Architecture",icon:ma},{name:"System Design",icon:Zd},{name:"Team Leadership",icon:Ao},{name:"Project Management",icon:V1},{name:"Technical Strategy",icon:P1}]},{title:"Core Technologies",skills:[{name:"Microservices",icon:T1},{name:"DevOps",icon:R1},{name:"Kubernetes",icon:ma},{name:"TensorFlow/PyTorch",icon:Ci},{name:"AWS/Azure",icon:ma},{name:"CI/CD",icon:j1}]},{title:"Development",skills:[{name:"Java/Python/Go",icon:bl},{name:"React/Node.js",icon:C1},{name:"GraphQL",icon:Vl},{name:"MongoDB/PostgreSQL",icon:Vl},{name:"API Design",icon:Js},{name:"System Integration",icon:D1}]}],yp=[{name:"English",level:"Professional",visible:!0},{name:"Hindi",level:"Professional",visible:!0},{name:"Telugu",level:"Native",visible:!1}],ru=[{name:"TOGAF® Standard, Version 9.2",issuer:"The Open Group",icon:jn,url:"https://www.credly.com/badges/8a84109e-4f76-463c-942c-083337b5e73c?source=linked_in_profile"},{name:"Generative AI Certification",issuer:"Growthschool",icon:Ci},{name:"Oracle 9i DBA",issuer:"Oracle",icon:Vl}],ak={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},vp=e=>{let t;const n=new Set,r=(c,p)=>{const f=typeof c=="function"?c(t):c;if(!Object.is(f,t)){const y=t;t=p??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(v=>v(t,y))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{(ak?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,i,l);return l},lk=e=>e?vp(e):vp;var w0={exports:{}},S0={},k0={exports:{}},E0={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kr=w;function uk(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ck=typeof Object.is=="function"?Object.is:uk,dk=kr.useState,fk=kr.useEffect,pk=kr.useLayoutEffect,hk=kr.useDebugValue;function mk(e,t){var n=t(),r=dk({inst:{value:n,getSnapshot:t}}),i=r[0].inst,s=r[1];return pk(function(){i.value=n,i.getSnapshot=t,La(i)&&s({inst:i})},[e,n,t]),fk(function(){return La(i)&&s({inst:i}),e(function(){La(i)&&s({inst:i})})},[e]),hk(n),n}function La(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ck(e,n)}catch{return!0}}function gk(e,t){return t()}var yk=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?gk:mk;E0.useSyncExternalStore=kr.useSyncExternalStore!==void 0?kr.useSyncExternalStore:yk;k0.exports=E0;var vk=k0.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oo=w,xk=vk;function wk(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Sk=typeof Object.is=="function"?Object.is:wk,kk=xk.useSyncExternalStore,Ek=Oo.useRef,Ck=Oo.useEffect,Pk=Oo.useMemo,Tk=Oo.useDebugValue;S0.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var s=Ek(null);if(s.current===null){var o={hasValue:!1,value:null};s.current=o}else o=s.current;s=Pk(function(){function l(y){if(!u){if(u=!0,c=y,y=r(y),i!==void 0&&o.hasValue){var v=o.value;if(i(v,y))return p=v}return p=y}if(v=p,Sk(c,y))return v;var x=r(y);return i!==void 0&&i(v,x)?v:(c=y,p=x)}var u=!1,c,p,f=n===void 0?null:n;return[function(){return l(t())},f===null?void 0:function(){return l(f())}]},[t,n,r,i]);var a=kk(e,s[0],s[1]);return Ck(function(){o.hasValue=!0,o.value=a},[a]),Tk(a),a};w0.exports=S0;var Nk=w0.exports;const jk=Tp(Nk),C0={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},{useDebugValue:Mk}=_,{useSyncExternalStoreWithSelector:Ak}=jk;let xp=!1;const Lk=e=>e;function Dk(e,t=Lk,n){(C0?"production":void 0)!=="production"&&n&&!xp&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),xp=!0);const r=Ak(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Mk(r),r}const Rk=e=>{(C0?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?lk(e):e,n=(r,i)=>Dk(t,r,i);return Object.assign(n,t),n},bk=e=>Rk,Vk={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1};function _k(e,t){let n;try{n=e()}catch{return}return{getItem:i=>{var s;const o=l=>l===null?null:JSON.parse(l,void 0),a=(s=n.getItem(i))!=null?s:null;return a instanceof Promise?a.then(o):o(a)},setItem:(i,s)=>n.setItem(i,JSON.stringify(s,void 0)),removeItem:i=>n.removeItem(i)}}const Mi=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return Mi(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return Mi(r)(n)}}}},Ok=(e,t)=>(n,r,i)=>{let s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:S=>S,version:0,merge:(S,m)=>({...m,...S}),...t},o=!1;const a=new Set,l=new Set;let u;try{u=s.getStorage()}catch{}if(!u)return e((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...S)},r,i);const c=Mi(s.serialize),p=()=>{const S=s.partialize({...r()});let m;const h=c({state:S,version:s.version}).then(g=>u.setItem(s.name,g)).catch(g=>{m=g});if(m)throw m;return h},f=i.setState;i.setState=(S,m)=>{f(S,m),p()};const y=e((...S)=>{n(...S),p()},r,i);let v;const x=()=>{var S;if(!u)return;o=!1,a.forEach(h=>h(r()));const m=((S=s.onRehydrateStorage)==null?void 0:S.call(s,r()))||void 0;return Mi(u.getItem.bind(u))(s.name).then(h=>{if(h)return s.deserialize(h)}).then(h=>{if(h)if(typeof h.version=="number"&&h.version!==s.version){if(s.migrate)return s.migrate(h.state,h.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return h.state}).then(h=>{var g;return v=s.merge(h,(g=r())!=null?g:y),n(v,!0),p()}).then(()=>{m==null||m(v,void 0),o=!0,l.forEach(h=>h(v))}).catch(h=>{m==null||m(void 0,h)})};return i.persist={setOptions:S=>{s={...s,...S},S.getStorage&&(u=S.getStorage())},clearStorage:()=>{u==null||u.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>x(),hasHydrated:()=>o,onHydrate:S=>(a.add(S),()=>{a.delete(S)}),onFinishHydration:S=>(l.add(S),()=>{l.delete(S)})},x(),v||y},$k=(e,t)=>(n,r,i)=>{let s={storage:_k(()=>localStorage),partialize:x=>x,version:0,merge:(x,S)=>({...S,...x}),...t},o=!1;const a=new Set,l=new Set;let u=s.storage;if(!u)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...x)},r,i);const c=()=>{const x=s.partialize({...r()});return u.setItem(s.name,{state:x,version:s.version})},p=i.setState;i.setState=(x,S)=>{p(x,S),c()};const f=e((...x)=>{n(...x),c()},r,i);i.getInitialState=()=>f;let y;const v=()=>{var x,S;if(!u)return;o=!1,a.forEach(h=>{var g;return h((g=r())!=null?g:f)});const m=((S=s.onRehydrateStorage)==null?void 0:S.call(s,(x=r())!=null?x:f))||void 0;return Mi(u.getItem.bind(u))(s.name).then(h=>{if(h)if(typeof h.version=="number"&&h.version!==s.version){if(s.migrate)return[!0,s.migrate(h.state,h.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,h.state];return[!1,void 0]}).then(h=>{var g;const[k,E]=h;if(y=s.merge(E,(g=r())!=null?g:f),n(y,!0),k)return c()}).then(()=>{m==null||m(y,void 0),y=r(),o=!0,l.forEach(h=>h(y))}).catch(h=>{m==null||m(void 0,h)})};return i.persist={setOptions:x=>{s={...s,...x},x.storage&&(u=x.storage)},clearStorage:()=>{u==null||u.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>v(),hasHydrated:()=>o,onHydrate:x=>(a.add(x),()=>{a.delete(x)}),onFinishHydration:x=>(l.add(x),()=>{l.delete(x)})},s.skipHydration||v(),y||f},Fk=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((Vk?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),Ok(e,t)):$k(e,t),Ik=Fk,Nr=bk()(Ik((e,t)=>({edits:{},updateContent:(n,r)=>e(i=>({edits:{...i.edits,[n]:r}})),getContent:(n,r)=>r}),{name:"content-storage"})),zk=()=>{const e=[{name:"Antler",imgSrc:"/images/antler.svg"},{name:"Infinity Learn",imgSrc:"/images/infinity-learn.webp"},{name:"IBM",imgSrc:"/images/IBM.png"},{name:"UBS",imgSrc:"/images/UBS_Logo.png"},{name:"Wipro",imgSrc:"/images/wipro.png"},{name:"Trice",imgSrc:"/images/trice.jpeg"}];return d.jsx("div",{className:"flex flex-wrap justify-center gap-6 mb-8",children:e.map((t,n)=>d.jsxs("div",{className:"glass-effect rounded-xl p-3 flex flex-col items-center",children:[d.jsx("div",{className:"w-16 h-16 mb-2 flex items-center justify-center",children:d.jsx("img",{src:t.imgSrc,alt:`${t.name} logo`,className:"max-w-full max-h-full object-contain"})}),d.jsx("span",{className:"text-xs text-gray-700",children:t.name})]},n))})};function F({content:e,className:t="",onSave:n,multiline:r=!1}){const[i,s]=w.useState(!1),[o,a]=w.useState(e),l=()=>{a(e),s(!0)},u=()=>{n&&n(o),s(!1)},c=()=>{s(!1)},p=f=>{f.key==="Enter"&&!f.shiftKey&&!r?(f.preventDefault(),u()):f.key==="Escape"&&(f.preventDefault(),c())};return i?d.jsxs("div",{className:"relative",children:[r?d.jsx("textarea",{value:o,onChange:f=>a(f.target.value),onKeyDown:p,className:`w-full p-2 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 ${t}`,rows:3,autoFocus:!0}):d.jsx("input",{type:"text",value:o,onChange:f=>a(f.target.value),onKeyDown:p,className:`w-full p-2 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 ${t}`,autoFocus:!0}),d.jsxs("div",{className:"flex mt-2 gap-2 justify-end",children:[d.jsx("button",{onClick:c,className:"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors",children:"Cancel"}),d.jsx("button",{onClick:u,className:"px-3 py-1 text-sm bg-primary-500 text-white hover:bg-primary-600 rounded transition-colors",children:"Save"})]})]}):d.jsx("div",{className:`${t} ${n?"cursor-pointer hover:bg-primary-50 hover:px-2 rounded transition-all":""}`,onClick:n?l:void 0,children:e})}var Bk=Object.defineProperty,Uk=(e,t,n)=>t in e?Bk(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Da=(e,t,n)=>(Uk(e,typeof t!="symbol"?t+"":t,n),n);let Hk=class{constructor(){Da(this,"current",this.detect()),Da(this,"handoffState","pending"),Da(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},Lt=new Hk,Je=(e,t)=>{Lt.isServer?w.useEffect(e,t):w.useLayoutEffect(e,t)};function Dt(e){let t=w.useRef(e);return Je(()=>{t.current=e},[e]),t}let X=function(e){let t=Dt(e);return _.useCallback((...n)=>t.current(...n),[t])};function $o(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function Fn(){let e=[],t={addEventListener(n,r,i,s){return n.addEventListener(r,i,s),t.add(()=>n.removeEventListener(r,i,s))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(...n){let r=setTimeout(...n);return t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return $o(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,i){let s=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:i}),this.add(()=>{Object.assign(n.style,{[r]:s})})},group(n){let r=Fn();return n(r),this.add(()=>r.dispose())},add(n){return e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let i of e.splice(r,1))i()}},dispose(){for(let n of e.splice(0))n()}};return t}function Oc(){let[e]=w.useState(Fn);return w.useEffect(()=>()=>e.dispose(),[e]),e}function Wk(){let e=typeof document>"u";return"useSyncExternalStore"in oi?(t=>t.useSyncExternalStore)(oi)(()=>()=>{},()=>!1,()=>!e):!1}function jr(){let e=Wk(),[t,n]=w.useState(Lt.isHandoffComplete);return t&&Lt.isHandoffComplete===!1&&n(!1),w.useEffect(()=>{t!==!0&&n(!0)},[t]),w.useEffect(()=>Lt.handoff(),[]),e?!1:t}var wp;let Mr=(wp=_.useId)!=null?wp:function(){let e=jr(),[t,n]=_.useState(e?()=>Lt.nextId():null);return Je(()=>{t===null&&n(Lt.nextId())},[t]),t!=null?""+t:void 0};function Se(e,t,...n){if(e in t){let i=t[e];return typeof i=="function"?i(...n):i}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(i=>`"${i}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,Se),r}function P0(e){return Lt.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let iu=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var wn=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(wn||{}),T0=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(T0||{}),Kk=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(Kk||{});function Gk(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(iu)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var N0=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(N0||{});function Yk(e,t=0){var n;return e===((n=P0(e))==null?void 0:n.body)?!1:Se(t,{0(){return e.matches(iu)},1(){let r=e;for(;r!==null;){if(r.matches(iu))return!0;r=r.parentElement}return!1}})}var Qk=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Qk||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function An(e){e==null||e.focus({preventScroll:!0})}let Xk=["textarea","input"].join(",");function qk(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,Xk))!=null?n:!1}function Zk(e,t=n=>n){return e.slice().sort((n,r)=>{let i=t(n),s=t(r);if(i===null||s===null)return 0;let o=i.compareDocumentPosition(s);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function js(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:i=[]}={}){let s=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,o=Array.isArray(e)?n?Zk(e):e:Gk(e);i.length>0&&o.length>1&&(o=o.filter(y=>!i.includes(y))),r=r??s.activeElement;let a=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),l=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,o.indexOf(r))-1;if(t&4)return Math.max(0,o.indexOf(r))+1;if(t&8)return o.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=t&32?{preventScroll:!0}:{},c=0,p=o.length,f;do{if(c>=p||c+p<=0)return 0;let y=l+c;if(t&16)y=(y+p)%p;else{if(y<0)return 3;if(y>=p)return 1}f=o[y],f==null||f.focus(u),c+=a}while(f!==s.activeElement);return t&6&&qk(f)&&f.select(),2}function j0(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Jk(){return/Android/gi.test(window.navigator.userAgent)}function eE(){return j0()||Jk()}function us(e,t,n){let r=Dt(t);w.useEffect(()=>{function i(s){r.current(s)}return document.addEventListener(e,i,n),()=>document.removeEventListener(e,i,n)},[e,n])}function M0(e,t,n){let r=Dt(t);w.useEffect(()=>{function i(s){r.current(s)}return window.addEventListener(e,i,n),()=>window.removeEventListener(e,i,n)},[e,n])}function tE(e,t,n=!0){let r=w.useRef(!1);w.useEffect(()=>{requestAnimationFrame(()=>{r.current=n})},[n]);function i(o,a){if(!r.current||o.defaultPrevented)return;let l=a(o);if(l===null||!l.getRootNode().contains(l)||!l.isConnected)return;let u=function c(p){return typeof p=="function"?c(p()):Array.isArray(p)||p instanceof Set?p:[p]}(e);for(let c of u){if(c===null)continue;let p=c instanceof HTMLElement?c:c.current;if(p!=null&&p.contains(l)||o.composed&&o.composedPath().includes(p))return}return!Yk(l,N0.Loose)&&l.tabIndex!==-1&&o.preventDefault(),t(o,l)}let s=w.useRef(null);us("pointerdown",o=>{var a,l;r.current&&(s.current=((l=(a=o.composedPath)==null?void 0:a.call(o))==null?void 0:l[0])||o.target)},!0),us("mousedown",o=>{var a,l;r.current&&(s.current=((l=(a=o.composedPath)==null?void 0:a.call(o))==null?void 0:l[0])||o.target)},!0),us("click",o=>{eE()||s.current&&(i(o,()=>s.current),s.current=null)},!0),us("touchend",o=>i(o,()=>o.target instanceof HTMLElement?o.target:null),!0),M0("blur",o=>i(o,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function $i(...e){return w.useMemo(()=>P0(...e),[...e])}let A0=Symbol();function nE(e,t=!0){return Object.assign(e,{[A0]:t})}function ut(...e){let t=w.useRef(e);w.useEffect(()=>{t.current=e},[e]);let n=X(r=>{for(let i of t.current)i!=null&&(typeof i=="function"?i(r):i.current=r)});return e.every(r=>r==null||(r==null?void 0:r[A0]))?void 0:n}function $c(e,t){let n=w.useRef([]),r=X(e);w.useEffect(()=>{let i=[...n.current];for(let[s,o]of t.entries())if(n.current[s]!==o){let a=r(t,i);return n.current=t,a}},[r,...t])}function lo(...e){return Array.from(new Set(e.flatMap(t=>typeof t=="string"?t.split(" "):[]))).filter(Boolean).join(" ")}var uo=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(uo||{}),Qt=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(Qt||{});function et({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:i,visible:s=!0,name:o,mergeRefs:a}){a=a??rE;let l=L0(t,e);if(s)return cs(l,n,r,o,a);let u=i??0;if(u&2){let{static:c=!1,...p}=l;if(c)return cs(p,n,r,o,a)}if(u&1){let{unmount:c=!0,...p}=l;return Se(c?0:1,{0(){return null},1(){return cs({...p,hidden:!0,style:{display:"none"}},n,r,o,a)}})}return cs(l,n,r,o,a)}function cs(e,t={},n,r,i){let{as:s=n,children:o,refName:a="ref",...l}=Ra(e,["unmount","static"]),u=e.ref!==void 0?{[a]:e.ref}:{},c=typeof o=="function"?o(t):o;"className"in l&&l.className&&typeof l.className=="function"&&(l.className=l.className(t));let p={};if(t){let f=!1,y=[];for(let[v,x]of Object.entries(t))typeof x=="boolean"&&(f=!0),x===!0&&y.push(v);f&&(p["data-headlessui-state"]=y.join(" "))}if(s===w.Fragment&&Object.keys(Sp(l)).length>0){if(!w.isValidElement(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(l).map(x=>`  - ${x}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(x=>`  - ${x}`).join(`
`)].join(`
`));let f=c.props,y=typeof(f==null?void 0:f.className)=="function"?(...x)=>lo(f==null?void 0:f.className(...x),l.className):lo(f==null?void 0:f.className,l.className),v=y?{className:y}:{};return w.cloneElement(c,Object.assign({},L0(c.props,Sp(Ra(l,["ref"]))),p,u,{ref:i(c.ref,u.ref)},v))}return w.createElement(s,Object.assign({},Ra(l,["ref"]),s!==w.Fragment&&u,s!==w.Fragment&&p),c)}function rE(...e){return e.every(t=>t==null)?void 0:t=>{for(let n of e)n!=null&&(typeof n=="function"?n(t):n.current=t)}}function L0(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?(n[i]!=null||(n[i]=[]),n[i].push(r[i])):t[i]=r[i];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](i,...s){let o=n[r];for(let a of o){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;a(i,...s)}}});return t}function Ue(e){var t;return Object.assign(w.forwardRef(e),{displayName:(t=e.displayName)!=null?t:e.name})}function Sp(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function Ra(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}let iE="div";var co=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(co||{});function sE(e,t){var n;let{features:r=1,...i}=e,s={ref:t,"aria-hidden":(r&2)===2?!0:(n=i["aria-hidden"])!=null?n:void 0,hidden:(r&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return et({ourProps:s,theirProps:i,slot:{},defaultTag:iE,name:"Hidden"})}let su=Ue(sE),Fc=w.createContext(null);Fc.displayName="OpenClosedContext";var _e=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(_e||{});function Ic(){return w.useContext(Fc)}function oE({value:e,children:t}){return _.createElement(Fc.Provider,{value:e},t)}function aE(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let Kt=[];aE(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&Kt[0]!==t.target&&(Kt.unshift(t.target),Kt=Kt.filter(n=>n!=null&&n.isConnected),Kt.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function lE(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(t==null?void 0:t.getAttribute("disabled"))==="";return r&&uE(n)?!1:r}function uE(e){if(!e)return!1;let t=e.previousElementSibling;for(;t!==null;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}var D0=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(D0||{});function R0(e,t,n,r){let i=Dt(n);w.useEffect(()=>{e=e??window;function s(o){i.current(o)}return e.addEventListener(t,s,r),()=>e.removeEventListener(t,s,r)},[e,t,r])}function Fi(){let e=w.useRef(!1);return Je(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function b0(e){let t=X(e),n=w.useRef(!1);w.useEffect(()=>(n.current=!1,()=>{n.current=!0,$o(()=>{n.current&&t()})}),[t])}var Gr=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Gr||{});function cE(){let e=w.useRef(0);return M0("keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function V0(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}let dE="div";var _0=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(_0||{});function fE(e,t){let n=w.useRef(null),r=ut(n,t),{initialFocus:i,containers:s,features:o=30,...a}=e;jr()||(o=1);let l=$i(n);mE({ownerDocument:l},!!(o&16));let u=gE({ownerDocument:l,container:n,initialFocus:i},!!(o&2));yE({ownerDocument:l,container:n,containers:s,previousActiveElement:u},!!(o&8));let c=cE(),p=X(x=>{let S=n.current;S&&(m=>m())(()=>{Se(c.current,{[Gr.Forwards]:()=>{js(S,wn.First,{skipElements:[x.relatedTarget]})},[Gr.Backwards]:()=>{js(S,wn.Last,{skipElements:[x.relatedTarget]})}})})}),f=Oc(),y=w.useRef(!1),v={ref:r,onKeyDown(x){x.key=="Tab"&&(y.current=!0,f.requestAnimationFrame(()=>{y.current=!1}))},onBlur(x){let S=V0(s);n.current instanceof HTMLElement&&S.add(n.current);let m=x.relatedTarget;m instanceof HTMLElement&&m.dataset.headlessuiFocusGuard!=="true"&&(O0(S,m)||(y.current?js(n.current,Se(c.current,{[Gr.Forwards]:()=>wn.Next,[Gr.Backwards]:()=>wn.Previous})|wn.WrapAround,{relativeTo:x.target}):x.target instanceof HTMLElement&&An(x.target)))}};return _.createElement(_.Fragment,null,!!(o&4)&&_.createElement(su,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:co.Focusable}),et({ourProps:v,theirProps:a,defaultTag:dE,name:"FocusTrap"}),!!(o&4)&&_.createElement(su,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:co.Focusable}))}let pE=Ue(fE),Fr=Object.assign(pE,{features:_0});function hE(e=!0){let t=w.useRef(Kt.slice());return $c(([n],[r])=>{r===!0&&n===!1&&$o(()=>{t.current.splice(0)}),r===!1&&n===!0&&(t.current=Kt.slice())},[e,Kt,t]),X(()=>{var n;return(n=t.current.find(r=>r!=null&&r.isConnected))!=null?n:null})}function mE({ownerDocument:e},t){let n=hE(t);$c(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&An(n())},[t]),b0(()=>{t&&An(n())})}function gE({ownerDocument:e,container:t,initialFocus:n},r){let i=w.useRef(null),s=Fi();return $c(()=>{if(!r)return;let o=t.current;o&&$o(()=>{if(!s.current)return;let a=e==null?void 0:e.activeElement;if(n!=null&&n.current){if((n==null?void 0:n.current)===a){i.current=a;return}}else if(o.contains(a)){i.current=a;return}n!=null&&n.current?An(n.current):js(o,wn.First)===T0.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),i.current=e==null?void 0:e.activeElement})},[r]),i}function yE({ownerDocument:e,container:t,containers:n,previousActiveElement:r},i){let s=Fi();R0(e==null?void 0:e.defaultView,"focus",o=>{if(!i||!s.current)return;let a=V0(n);t.current instanceof HTMLElement&&a.add(t.current);let l=r.current;if(!l)return;let u=o.target;u&&u instanceof HTMLElement?O0(a,u)?(r.current=u,An(u)):(o.preventDefault(),o.stopPropagation(),An(l)):An(r.current)},!0)}function O0(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let $0=w.createContext(!1);function vE(){return w.useContext($0)}function ou(e){return _.createElement($0.Provider,{value:e.force},e.children)}function xE(e){let t=vE(),n=w.useContext(F0),r=$i(e),[i,s]=w.useState(()=>{if(!t&&n!==null||Lt.isServer)return null;let o=r==null?void 0:r.getElementById("headlessui-portal-root");if(o)return o;if(r===null)return null;let a=r.createElement("div");return a.setAttribute("id","headlessui-portal-root"),r.body.appendChild(a)});return w.useEffect(()=>{i!==null&&(r!=null&&r.body.contains(i)||r==null||r.body.appendChild(i))},[i,r]),w.useEffect(()=>{t||n!==null&&s(n.current)},[n,s,t]),i}let wE=w.Fragment;function SE(e,t){let n=e,r=w.useRef(null),i=ut(nE(c=>{r.current=c}),t),s=$i(r),o=xE(r),[a]=w.useState(()=>{var c;return Lt.isServer?null:(c=s==null?void 0:s.createElement("div"))!=null?c:null}),l=w.useContext(au),u=jr();return Je(()=>{!o||!a||o.contains(a)||(a.setAttribute("data-headlessui-portal",""),o.appendChild(a))},[o,a]),Je(()=>{if(a&&l)return l.register(a)},[l,a]),b0(()=>{var c;!o||!a||(a instanceof Node&&o.contains(a)&&o.removeChild(a),o.childNodes.length<=0&&((c=o.parentElement)==null||c.removeChild(o)))}),u?!o||!a?null:Fm.createPortal(et({ourProps:{ref:i},theirProps:n,defaultTag:wE,name:"Portal"}),a):null}let kE=w.Fragment,F0=w.createContext(null);function EE(e,t){let{target:n,...r}=e,i={ref:ut(t)};return _.createElement(F0.Provider,{value:n},et({ourProps:i,theirProps:r,defaultTag:kE,name:"Popover.Group"}))}let au=w.createContext(null);function CE(){let e=w.useContext(au),t=w.useRef([]),n=X(s=>(t.current.push(s),e&&e.register(s),()=>r(s))),r=X(s=>{let o=t.current.indexOf(s);o!==-1&&t.current.splice(o,1),e&&e.unregister(s)}),i=w.useMemo(()=>({register:n,unregister:r,portals:t}),[n,r,t]);return[t,w.useMemo(()=>function({children:s}){return _.createElement(au.Provider,{value:i},s)},[i])]}let PE=Ue(SE),TE=Ue(EE),lu=Object.assign(PE,{Group:TE});function NE(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const jE=typeof Object.is=="function"?Object.is:NE,{useState:ME,useEffect:AE,useLayoutEffect:LE,useDebugValue:DE}=oi;function RE(e,t,n){const r=t(),[{inst:i},s]=ME({inst:{value:r,getSnapshot:t}});return LE(()=>{i.value=r,i.getSnapshot=t,ba(i)&&s({inst:i})},[e,r,t]),AE(()=>(ba(i)&&s({inst:i}),e(()=>{ba(i)&&s({inst:i})})),[e]),DE(r),r}function ba(e){const t=e.getSnapshot,n=e.value;try{const r=t();return!jE(n,r)}catch{return!0}}function bE(e,t,n){return t()}const VE=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",_E=!VE,OE=_E?bE:RE,$E="useSyncExternalStore"in oi?(e=>e.useSyncExternalStore)(oi):OE;function FE(e){return $E(e.subscribe,e.getSnapshot,e.getSnapshot)}function IE(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(i){return r.add(i),()=>r.delete(i)},dispatch(i,...s){let o=t[i].call(n,...s);o&&(n=o,r.forEach(a=>a()))}}}function zE(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,i=r.clientWidth-r.offsetWidth,s=e-i;n.style(r,"paddingRight",`${s}px`)}}}function BE(){return j0()?{before({doc:e,d:t,meta:n}){function r(i){return n.containers.flatMap(s=>s()).some(s=>s.contains(i))}t.microTask(()=>{var i;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let a=Fn();a.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>a.dispose()))}let s=(i=window.scrollY)!=null?i:window.pageYOffset,o=null;t.addEventListener(e,"click",a=>{if(a.target instanceof HTMLElement)try{let l=a.target.closest("a");if(!l)return;let{hash:u}=new URL(l.href),c=e.querySelector(u);c&&!r(c)&&(o=c)}catch{}},!0),t.addEventListener(e,"touchstart",a=>{if(a.target instanceof HTMLElement)if(r(a.target)){let l=a.target;for(;l.parentElement&&r(l.parentElement);)l=l.parentElement;t.style(l,"overscrollBehavior","contain")}else t.style(a.target,"touchAction","none")}),t.addEventListener(e,"touchmove",a=>{if(a.target instanceof HTMLElement)if(r(a.target)){let l=a.target;for(;l.parentElement&&l.dataset.headlessuiPortal!==""&&!(l.scrollHeight>l.clientHeight||l.scrollWidth>l.clientWidth);)l=l.parentElement;l.dataset.headlessuiPortal===""&&a.preventDefault()}else a.preventDefault()},{passive:!1}),t.add(()=>{var a;let l=(a=window.scrollY)!=null?a:window.pageYOffset;s!==l&&window.scrollTo(0,s),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{}}function UE(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function HE(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Pn=IE(()=>new Map,{PUSH(e,t){var n;let r=(n=this.get(e))!=null?n:{doc:e,count:0,d:Fn(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:HE(n)},i=[BE(),zE(),UE()];i.forEach(({before:s})=>s==null?void 0:s(r)),i.forEach(({after:s})=>s==null?void 0:s(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});Pn.subscribe(()=>{let e=Pn.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let r=t.get(n.doc)==="hidden",i=n.count!==0;(i&&!r||!i&&r)&&Pn.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&Pn.dispatch("TEARDOWN",n)}});function WE(e,t,n){let r=FE(Pn),i=e?r.get(e):void 0,s=i?i.count>0:!1;return Je(()=>{if(!(!e||!t))return Pn.dispatch("PUSH",e,n),()=>Pn.dispatch("POP",e,n)},[t,e]),s}let Va=new Map,Ir=new Map;function kp(e,t=!0){Je(()=>{var n;if(!t)return;let r=typeof e=="function"?e():e.current;if(!r)return;function i(){var o;if(!r)return;let a=(o=Ir.get(r))!=null?o:1;if(a===1?Ir.delete(r):Ir.set(r,a-1),a!==1)return;let l=Va.get(r);l&&(l["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",l["aria-hidden"]),r.inert=l.inert,Va.delete(r))}let s=(n=Ir.get(r))!=null?n:0;return Ir.set(r,s+1),s!==0||(Va.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),i},[e,t])}function KE({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let i=w.useRef((r=n==null?void 0:n.current)!=null?r:null),s=$i(i),o=X(()=>{var a,l,u;let c=[];for(let p of e)p!==null&&(p instanceof HTMLElement?c.push(p):"current"in p&&p.current instanceof HTMLElement&&c.push(p.current));if(t!=null&&t.current)for(let p of t.current)c.push(p);for(let p of(a=s==null?void 0:s.querySelectorAll("html > *, body > *"))!=null?a:[])p!==document.body&&p!==document.head&&p instanceof HTMLElement&&p.id!=="headlessui-portal-root"&&(p.contains(i.current)||p.contains((u=(l=i.current)==null?void 0:l.getRootNode())==null?void 0:u.host)||c.some(f=>p.contains(f))||c.push(p));return c});return{resolveContainers:o,contains:X(a=>o().some(l=>l.contains(a))),mainTreeNodeRef:i,MainTreeNode:w.useMemo(()=>function(){return n!=null?null:_.createElement(su,{features:co.Hidden,ref:i})},[i,n])}}let zc=w.createContext(()=>{});zc.displayName="StackContext";var uu=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(uu||{});function GE(){return w.useContext(zc)}function YE({children:e,onUpdate:t,type:n,element:r,enabled:i}){let s=GE(),o=X((...a)=>{t==null||t(...a),s(...a)});return Je(()=>{let a=i===void 0||i===!0;return a&&o(0,n,r),()=>{a&&o(1,n,r)}},[o,n,r,i]),_.createElement(zc.Provider,{value:o},e)}let I0=w.createContext(null);function z0(){let e=w.useContext(I0);if(e===null){let t=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,z0),t}return e}function QE(){let[e,t]=w.useState([]);return[e.length>0?e.join(" "):void 0,w.useMemo(()=>function(n){let r=X(s=>(t(o=>[...o,s]),()=>t(o=>{let a=o.slice(),l=a.indexOf(s);return l!==-1&&a.splice(l,1),a}))),i=w.useMemo(()=>({register:r,slot:n.slot,name:n.name,props:n.props}),[r,n.slot,n.name,n.props]);return _.createElement(I0.Provider,{value:i},n.children)},[t])]}let XE="p";function qE(e,t){let n=Mr(),{id:r=`headlessui-description-${n}`,...i}=e,s=z0(),o=ut(t);Je(()=>s.register(r),[r,s.register]);let a={ref:o,...s.props,id:r};return et({ourProps:a,theirProps:i,slot:s.slot||{},defaultTag:XE,name:s.name||"Description"})}let ZE=Ue(qE),JE=Object.assign(ZE,{});var eC=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(eC||{}),tC=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(tC||{});let nC={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},fo=w.createContext(null);fo.displayName="DialogContext";function Ii(e){let t=w.useContext(fo);if(t===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Ii),n}return t}function rC(e,t,n=()=>[document.body]){WE(e,t,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],n]}})}function iC(e,t){return Se(t.type,nC,e,t)}let sC="div",oC=uo.RenderStrategy|uo.Static;function aC(e,t){let n=Mr(),{id:r=`headlessui-dialog-${n}`,open:i,onClose:s,initialFocus:o,role:a="dialog",__demoMode:l=!1,...u}=e,[c,p]=w.useState(0),f=w.useRef(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(f.current||(f.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let y=Ic();i===void 0&&y!==null&&(i=(y&_e.Open)===_e.Open);let v=w.useRef(null),x=ut(v,t),S=$i(v),m=e.hasOwnProperty("open")||y!==null,h=e.hasOwnProperty("onClose");if(!m&&!h)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!m)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!h)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof i!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${i}`);if(typeof s!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${s}`);let g=i?0:1,[k,E]=w.useReducer(iC,{titleId:null,descriptionId:null,panelRef:w.createRef()}),T=X(()=>s(!1)),j=X(te=>E({type:0,id:te})),C=jr()?l?!1:g===0:!1,b=c>1,A=w.useContext(fo)!==null,[Y,Ce]=CE(),be={get current(){var te;return(te=k.panelRef.current)!=null?te:v.current}},{resolveContainers:tt,mainTreeNodeRef:ct,MainTreeNode:$t}=KE({portals:Y,defaultContainers:[be]}),B=b?"parent":"leaf",N=y!==null?(y&_e.Closing)===_e.Closing:!1,D=A||N?!1:C,R=w.useCallback(()=>{var te,xt;return(xt=Array.from((te=S==null?void 0:S.querySelectorAll("body > *"))!=null?te:[]).find(He=>He.id==="headlessui-portal-root"?!1:He.contains(ct.current)&&He instanceof HTMLElement))!=null?xt:null},[ct]);kp(R,D);let U=b?!0:C,H=w.useCallback(()=>{var te,xt;return(xt=Array.from((te=S==null?void 0:S.querySelectorAll("[data-headlessui-portal]"))!=null?te:[]).find(He=>He.contains(ct.current)&&He instanceof HTMLElement))!=null?xt:null},[ct]);kp(H,U),tE(tt,te=>{te.preventDefault(),T()},!(!C||b));let ue=!(b||g!==0);R0(S==null?void 0:S.defaultView,"keydown",te=>{ue&&(te.defaultPrevented||te.key===D0.Escape&&(te.preventDefault(),te.stopPropagation(),T()))}),rC(S,!(N||g!==0||A),tt),w.useEffect(()=>{if(g!==0||!v.current)return;let te=new ResizeObserver(xt=>{for(let He of xt){let Bi=He.target.getBoundingClientRect();Bi.x===0&&Bi.y===0&&Bi.width===0&&Bi.height===0&&T()}});return te.observe(v.current),()=>te.disconnect()},[g,v,T]);let[vt,zn]=QE(),W0=w.useMemo(()=>[{dialogState:g,close:T,setTitleId:j},k],[g,k,T,j]),Bc=w.useMemo(()=>({open:g===0}),[g]),K0={ref:x,id:r,role:a,"aria-modal":g===0?!0:void 0,"aria-labelledby":k.titleId,"aria-describedby":vt};return _.createElement(YE,{type:"Dialog",enabled:g===0,element:v,onUpdate:X((te,xt)=>{xt==="Dialog"&&Se(te,{[uu.Add]:()=>p(He=>He+1),[uu.Remove]:()=>p(He=>He-1)})})},_.createElement(ou,{force:!0},_.createElement(lu,null,_.createElement(fo.Provider,{value:W0},_.createElement(lu.Group,{target:v},_.createElement(ou,{force:!1},_.createElement(zn,{slot:Bc,name:"Dialog.Description"},_.createElement(Fr,{initialFocus:o,containers:tt,features:C?Se(B,{parent:Fr.features.RestoreFocus,leaf:Fr.features.All&~Fr.features.FocusLock}):Fr.features.None},_.createElement(Ce,null,et({ourProps:K0,theirProps:u,slot:Bc,defaultTag:sC,features:oC,visible:g===0,name:"Dialog"}))))))))),_.createElement($t,null))}let lC="div";function uC(e,t){let n=Mr(),{id:r=`headlessui-dialog-overlay-${n}`,...i}=e,[{dialogState:s,close:o}]=Ii("Dialog.Overlay"),a=ut(t),l=X(c=>{if(c.target===c.currentTarget){if(lE(c.currentTarget))return c.preventDefault();c.preventDefault(),c.stopPropagation(),o()}}),u=w.useMemo(()=>({open:s===0}),[s]);return et({ourProps:{ref:a,id:r,"aria-hidden":!0,onClick:l},theirProps:i,slot:u,defaultTag:lC,name:"Dialog.Overlay"})}let cC="div";function dC(e,t){let n=Mr(),{id:r=`headlessui-dialog-backdrop-${n}`,...i}=e,[{dialogState:s},o]=Ii("Dialog.Backdrop"),a=ut(t);w.useEffect(()=>{if(o.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[o.panelRef]);let l=w.useMemo(()=>({open:s===0}),[s]);return _.createElement(ou,{force:!0},_.createElement(lu,null,et({ourProps:{ref:a,id:r,"aria-hidden":!0},theirProps:i,slot:l,defaultTag:cC,name:"Dialog.Backdrop"})))}let fC="div";function pC(e,t){let n=Mr(),{id:r=`headlessui-dialog-panel-${n}`,...i}=e,[{dialogState:s},o]=Ii("Dialog.Panel"),a=ut(t,o.panelRef),l=w.useMemo(()=>({open:s===0}),[s]),u=X(c=>{c.stopPropagation()});return et({ourProps:{ref:a,id:r,onClick:u},theirProps:i,slot:l,defaultTag:fC,name:"Dialog.Panel"})}let hC="h2";function mC(e,t){let n=Mr(),{id:r=`headlessui-dialog-title-${n}`,...i}=e,[{dialogState:s,setTitleId:o}]=Ii("Dialog.Title"),a=ut(t);w.useEffect(()=>(o(r),()=>o(null)),[r,o]);let l=w.useMemo(()=>({open:s===0}),[s]);return et({ourProps:{ref:a,id:r},theirProps:i,slot:l,defaultTag:hC,name:"Dialog.Title"})}let gC=Ue(aC),yC=Ue(dC),vC=Ue(pC),xC=Ue(uC),wC=Ue(mC),_a=Object.assign(gC,{Backdrop:yC,Panel:vC,Overlay:xC,Title:wC,Description:JE});function SC(e=0){let[t,n]=w.useState(e),r=Fi(),i=w.useCallback(l=>{r.current&&n(u=>u|l)},[t,r]),s=w.useCallback(l=>!!(t&l),[t]),o=w.useCallback(l=>{r.current&&n(u=>u&~l)},[n,r]),a=w.useCallback(l=>{r.current&&n(u=>u^l)},[n]);return{flags:t,addFlag:i,hasFlag:s,removeFlag:o,toggleFlag:a}}function kC(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function Oa(e,...t){e&&t.length>0&&e.classList.add(...t)}function $a(e,...t){e&&t.length>0&&e.classList.remove(...t)}function EC(e,t){let n=Fn();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:i}=getComputedStyle(e),[s,o]=[r,i].map(l=>{let[u=0]=l.split(",").filter(Boolean).map(c=>c.includes("ms")?parseFloat(c):parseFloat(c)*1e3).sort((c,p)=>p-c);return u}),a=s+o;if(a!==0){n.group(u=>{u.setTimeout(()=>{t(),u.dispose()},a),u.addEventListener(e,"transitionrun",c=>{c.target===c.currentTarget&&u.dispose()})});let l=n.addEventListener(e,"transitionend",u=>{u.target===u.currentTarget&&(t(),l())})}else t();return n.add(()=>t()),n.dispose}function CC(e,t,n,r){let i=n?"enter":"leave",s=Fn(),o=r!==void 0?kC(r):()=>{};i==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let a=Se(i,{enter:()=>t.enter,leave:()=>t.leave}),l=Se(i,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),u=Se(i,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return $a(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),Oa(e,...t.base,...a,...u),s.nextFrame(()=>{$a(e,...t.base,...a,...u),Oa(e,...t.base,...a,...l),EC(e,()=>($a(e,...t.base,...a),Oa(e,...t.base,...t.entered),o()))}),s.dispose}function PC({immediate:e,container:t,direction:n,classes:r,onStart:i,onStop:s}){let o=Fi(),a=Oc(),l=Dt(n);Je(()=>{e&&(l.current="enter")},[e]),Je(()=>{let u=Fn();a.add(u.dispose);let c=t.current;if(c&&l.current!=="idle"&&o.current)return u.dispose(),i.current(l.current),u.add(CC(c,r.current,l.current==="enter",()=>{u.dispose(),s.current(l.current)})),u.dispose},[n])}function It(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let Fo=w.createContext(null);Fo.displayName="TransitionContext";var TC=(e=>(e.Visible="visible",e.Hidden="hidden",e))(TC||{});function NC(){let e=w.useContext(Fo);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function jC(){let e=w.useContext(Io);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}let Io=w.createContext(null);Io.displayName="NestingContext";function zo(e){return"children"in e?zo(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t==="visible").length>0}function B0(e,t){let n=Dt(e),r=w.useRef([]),i=Fi(),s=Oc(),o=X((y,v=Qt.Hidden)=>{let x=r.current.findIndex(({el:S})=>S===y);x!==-1&&(Se(v,{[Qt.Unmount](){r.current.splice(x,1)},[Qt.Hidden](){r.current[x].state="hidden"}}),s.microTask(()=>{var S;!zo(r)&&i.current&&((S=n.current)==null||S.call(n))}))}),a=X(y=>{let v=r.current.find(({el:x})=>x===y);return v?v.state!=="visible"&&(v.state="visible"):r.current.push({el:y,state:"visible"}),()=>o(y,Qt.Unmount)}),l=w.useRef([]),u=w.useRef(Promise.resolve()),c=w.useRef({enter:[],leave:[],idle:[]}),p=X((y,v,x)=>{l.current.splice(0),t&&(t.chains.current[v]=t.chains.current[v].filter(([S])=>S!==y)),t==null||t.chains.current[v].push([y,new Promise(S=>{l.current.push(S)})]),t==null||t.chains.current[v].push([y,new Promise(S=>{Promise.all(c.current[v].map(([m,h])=>h)).then(()=>S())})]),v==="enter"?u.current=u.current.then(()=>t==null?void 0:t.wait.current).then(()=>x(v)):x(v)}),f=X((y,v,x)=>{Promise.all(c.current[v].splice(0).map(([S,m])=>m)).then(()=>{var S;(S=l.current.shift())==null||S()}).then(()=>x(v))});return w.useMemo(()=>({children:r,register:a,unregister:o,onStart:p,onStop:f,wait:u,chains:c}),[a,o,r,p,f,c,u])}function MC(){}let AC=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Ep(e){var t;let n={};for(let r of AC)n[r]=(t=e[r])!=null?t:MC;return n}function LC(e){let t=w.useRef(Ep(e));return w.useEffect(()=>{t.current=Ep(e)},[e]),t}let DC="div",U0=uo.RenderStrategy;function RC(e,t){var n,r;let{beforeEnter:i,afterEnter:s,beforeLeave:o,afterLeave:a,enter:l,enterFrom:u,enterTo:c,entered:p,leave:f,leaveFrom:y,leaveTo:v,...x}=e,S=w.useRef(null),m=ut(S,t),h=(n=x.unmount)==null||n?Qt.Unmount:Qt.Hidden,{show:g,appear:k,initial:E}=NC(),[T,j]=w.useState(g?"visible":"hidden"),C=jC(),{register:b,unregister:A}=C;w.useEffect(()=>b(S),[b,S]),w.useEffect(()=>{if(h===Qt.Hidden&&S.current){if(g&&T!=="visible"){j("visible");return}return Se(T,{hidden:()=>A(S),visible:()=>b(S)})}},[T,S,b,A,g,h]);let Y=Dt({base:It(x.className),enter:It(l),enterFrom:It(u),enterTo:It(c),entered:It(p),leave:It(f),leaveFrom:It(y),leaveTo:It(v)}),Ce=LC({beforeEnter:i,afterEnter:s,beforeLeave:o,afterLeave:a}),be=jr();w.useEffect(()=>{if(be&&T==="visible"&&S.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[S,T,be]);let tt=E&&!k,ct=k&&g&&E,$t=!be||tt?"idle":g?"enter":"leave",B=SC(0),N=X(ue=>Se(ue,{enter:()=>{B.addFlag(_e.Opening),Ce.current.beforeEnter()},leave:()=>{B.addFlag(_e.Closing),Ce.current.beforeLeave()},idle:()=>{}})),D=X(ue=>Se(ue,{enter:()=>{B.removeFlag(_e.Opening),Ce.current.afterEnter()},leave:()=>{B.removeFlag(_e.Closing),Ce.current.afterLeave()},idle:()=>{}})),R=B0(()=>{j("hidden"),A(S)},C),U=w.useRef(!1);PC({immediate:ct,container:S,classes:Y,direction:$t,onStart:Dt(ue=>{U.current=!0,R.onStart(S,ue,N)}),onStop:Dt(ue=>{U.current=!1,R.onStop(S,ue,D),ue==="leave"&&!zo(R)&&(j("hidden"),A(S))})});let H=x,hn={ref:m};return ct?H={...H,className:lo(x.className,...Y.current.enter,...Y.current.enterFrom)}:U.current&&(H.className=lo(x.className,(r=S.current)==null?void 0:r.className),H.className===""&&delete H.className),_.createElement(Io.Provider,{value:R},_.createElement(oE,{value:Se(T,{visible:_e.Open,hidden:_e.Closed})|B.flags},et({ourProps:hn,theirProps:H,defaultTag:DC,features:U0,visible:T==="visible",name:"Transition.Child"})))}function bC(e,t){let{show:n,appear:r=!1,unmount:i=!0,...s}=e,o=w.useRef(null),a=ut(o,t);jr();let l=Ic();if(n===void 0&&l!==null&&(n=(l&_e.Open)===_e.Open),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,c]=w.useState(n?"visible":"hidden"),p=B0(()=>{c("hidden")}),[f,y]=w.useState(!0),v=w.useRef([n]);Je(()=>{f!==!1&&v.current[v.current.length-1]!==n&&(v.current.push(n),y(!1))},[v,n]);let x=w.useMemo(()=>({show:n,appear:r,initial:f}),[n,r,f]);w.useEffect(()=>{if(n)c("visible");else if(!zo(p))c("hidden");else{let g=o.current;if(!g)return;let k=g.getBoundingClientRect();k.x===0&&k.y===0&&k.width===0&&k.height===0&&c("hidden")}},[n,p]);let S={unmount:i},m=X(()=>{var g;f&&y(!1),(g=e.beforeEnter)==null||g.call(e)}),h=X(()=>{var g;f&&y(!1),(g=e.beforeLeave)==null||g.call(e)});return _.createElement(Io.Provider,{value:p},_.createElement(Fo.Provider,{value:x},et({ourProps:{...S,as:w.Fragment,children:_.createElement(H0,{ref:a,...S,...s,beforeEnter:m,beforeLeave:h})},theirProps:{},defaultTag:w.Fragment,features:U0,visible:u==="visible",name:"Transition"})))}function VC(e,t){let n=w.useContext(Fo)!==null,r=Ic()!==null;return _.createElement(_.Fragment,null,!n&&r?_.createElement(cu,{ref:t,...e}):_.createElement(H0,{ref:t,...e}))}let cu=Ue(bC),H0=Ue(RC),_C=Ue(VC),Fa=Object.assign(cu,{Child:_C,Root:cu});function Bo({isOpen:e,onClose:t,title:n,children:r}){return d.jsx(Fa,{appear:!0,show:e,as:w.Fragment,children:d.jsxs(_a,{as:"div",className:"relative z-50",onClose:t,children:[d.jsx(Fa.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm"})}),d.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:d.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:d.jsx(Fa.Child,{as:w.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:d.jsxs(_a.Panel,{className:"w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-4 sm:p-6 text-left align-middle shadow-xl transition-all mx-4",children:[d.jsxs("div",{className:"flex justify-between items-center mb-4",children:[d.jsx(_a.Title,{as:"h3",className:"text-xl sm:text-2xl font-bold gradient-text pr-8",children:n}),d.jsx("button",{onClick:t,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:d.jsx(Km,{className:"w-5 h-5 sm:w-6 sm:h-6"})})]}),d.jsx("div",{className:"mt-4 max-h-[calc(100vh-12rem)] overflow-y-auto",children:r})]})})})})]})})}const OC={hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.2,delayChildren:.3}}},Cp={hidden:{opacity:0,y:20},show:{opacity:1,y:0}};function $C(){const[e,t]=w.useState(!1),{getContent:n,updateContent:r}=Nr(),i=n("hero.title","Director of Software Engineering"),s=n("hero.description","Driving innovation through Generative AI, scaling engineering teams, and aligning technology with business goals for global impact."),o=n("hero.phone1","+971 569543336"),a=n("hero.phone2","+91 9966933336"),l=n("hero.location","Hyderabad | Dubai");return d.jsxs("section",{id:"about",className:"relative min-h-[calc(100vh-4rem)] flex items-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-accent-50 px-4 sm:px-6",children:[d.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary-100/30 via-transparent to-transparent opacity-70"}),d.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-accent-100/30 via-transparent to-transparent opacity-70"}),d.jsx("div",{className:"absolute inset-0 grid-pattern opacity-10"}),d.jsx("div",{className:"relative w-full max-w-7xl mx-auto py-12 sm:py-20",children:d.jsxs(re.div,{variants:OC,initial:"hidden",animate:"show",className:"flex flex-col items-center",children:[d.jsxs(re.div,{variants:Cp,className:"w-full max-w-4xl mx-auto mb-16 text-center",children:[d.jsx("div",{className:"inline-block mb-3 px-4 py-1.5 bg-primary-50 rounded-full text-primary-700 text-sm font-medium",children:"Entrepreneur In Residence (DXB2)"}),d.jsxs("div",{className:"relative w-32 h-32 sm:w-40 sm:h-40 mx-auto mb-6",children:[d.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary-200 to-accent-200 rounded-full transform rotate-6 blur-[2px]"}),d.jsx("img",{src:"/images/image_suga.jpg",alt:"Surendra Ganne",className:"relative z-10 w-full h-full object-cover rounded-full shadow-xl border-4 border-white"})]}),d.jsx("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent",children:"Surendra Ganne"}),d.jsxs("div",{className:"flex items-center justify-center gap-2 mb-6",children:[d.jsx(jn,{className:"w-5 h-5 sm:w-6 sm:h-6 text-primary-600"}),d.jsx(F,{content:i,onSave:u=>r("hero.title",u),className:"text-lg sm:text-xl text-primary-600 font-medium"})]}),d.jsx("div",{className:"max-w-2xl mx-auto",children:d.jsx(F,{content:s,onSave:u=>r("hero.description",u),className:"text-base sm:text-lg md:text-xl text-gray-600 leading-relaxed",multiline:!0})})]}),d.jsxs(re.div,{variants:Cp,className:"w-full grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12",children:[d.jsxs("div",{className:"glass-effect rounded-2xl p-6 backdrop-blur-sm bg-white/70 flex flex-col h-full",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[d.jsx(jn,{className:"w-5 h-5 text-primary-600"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Certifications"})]}),d.jsx("div",{className:"flex flex-col gap-3 mt-2",children:ru.map((u,c)=>d.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white/50 rounded-xl hover:bg-primary-50/50 transition-colors",children:[d.jsx(u.icon,{className:"w-5 h-5 text-primary-600"}),d.jsx("span",{className:"text-gray-700 font-medium",children:u.name})]},c))})]}),d.jsxs("div",{className:"glass-effect rounded-2xl p-6 backdrop-blur-sm bg-white/70 flex flex-col h-full",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[d.jsx(ga,{className:"w-5 h-5 text-primary-600"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Contact Me"})]}),d.jsxs("div",{className:"flex flex-col gap-5 mt-2",children:[d.jsxs("div",{className:"flex items-start gap-3",children:[d.jsx($l,{className:"w-5 h-5 text-primary-600 mt-0.5"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Location"}),d.jsx(F,{content:l,onSave:u=>r("hero.location",u),className:"text-base font-medium text-gray-800"})]})]}),d.jsxs("div",{className:"flex items-start gap-3",children:[d.jsx(ga,{className:"w-5 h-5 text-primary-600 mt-0.5"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Primary"}),d.jsx(F,{content:o,onSave:u=>r("hero.phone1",u),className:"text-base font-medium text-gray-800"})]})]}),d.jsxs("div",{className:"flex items-start gap-3",children:[d.jsx(ga,{className:"w-5 h-5 text-primary-600 mt-0.5"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Secondary"}),d.jsx(F,{content:a,onSave:u=>r("hero.phone2",u),className:"text-base font-medium text-gray-800"})]})]}),d.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 mt-auto pt-4",children:[d.jsxs("a",{href:"mailto:<EMAIL>",className:"button-primary flex items-center justify-center gap-2 text-sm py-2.5 px-4 w-full",children:[d.jsx(eo,{className:"w-4 h-4"}),"Email Me"]}),d.jsxs("button",{onClick:()=>t(!0),className:"button-secondary flex items-center justify-center gap-2 text-sm py-2.5 px-4 w-full",children:[d.jsx(Dl,{className:"w-4 h-4"}),"Schedule Call"]})]})]})]}),d.jsxs("div",{className:"glass-effect rounded-2xl p-6 backdrop-blur-sm bg-white/70 flex flex-col h-full",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[d.jsx(k1,{className:"w-5 h-5 text-primary-600"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Worked With"})]}),d.jsx("div",{className:"flex-grow flex flex-col justify-center",children:d.jsx(zk,{})})]})]})]})}),d.jsx(Bo,{isOpen:e,onClose:()=>t(!1),title:"Schedule a Call",children:d.jsxs("div",{className:"space-y-6",children:[d.jsx("p",{className:"text-gray-600",children:"Choose your preferred meeting duration:"}),d.jsxs("div",{className:"grid sm:grid-cols-2 gap-4",children:[d.jsxs("a",{href:"https://tidycal.com/surendraganne/30-minute-meeting",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-3 p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors",children:[d.jsx(ef,{className:"w-5 h-5 text-primary-600"}),d.jsx("span",{className:"font-medium",children:"30 Minutes"})]}),d.jsxs("a",{href:"https://tidycal.com/surendraganne/60-minute-meeting",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-3 p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors",children:[d.jsx(ef,{className:"w-5 h-5 text-primary-600"}),d.jsx("span",{className:"font-medium",children:"60 Minutes"})]})]})]})})]})}const FC=[{icon:Ci,title:"AI & Innovation Leadership",description:"Drive 30% faster product delivery through AI integration and innovative solutions"},{icon:bl,title:"Product Development Expert",description:"15+ years building scalable web/mobile applications and end-to-end product development"},{icon:Js,title:"Global Team Leadership",description:"Led 50+ engineers across 3 countries, boosting productivity by 40%"}];function IC(){const{getContent:e,updateContent:t}=Nr();return d.jsx("section",{className:"py-20 bg-white",children:d.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs("div",{className:"text-center mb-12",children:[d.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Value Proposition"}),d.jsx(F,{content:e("summary.description","Product Development Expert | AI Innovation Leader | Global Team Builder specializing in scalable web & mobile applications, AI integration, and end-to-end product development"),onSave:n=>t("summary.description",n),className:"text-gray-600 max-w-3xl mx-auto leading-relaxed",multiline:!0})]}),d.jsx("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:FC.map((n,r)=>d.jsx("div",{className:"glass-effect rounded-xl p-8 card-hover",style:{animationDelay:`${r*200}ms`},children:d.jsxs("div",{className:"flex flex-col items-center text-center space-y-4",children:[d.jsx("div",{className:"p-4 bg-primary-50 rounded-full",children:d.jsx(n.icon,{className:"w-8 h-8 text-primary-600"})}),d.jsx(F,{content:e(`summary.prop.${r}.title`,n.title),onSave:i=>t(`summary.prop.${r}.title`,i),className:"text-xl font-bold text-gray-900"}),d.jsx(F,{content:e(`summary.prop.${r}.description`,n.description),onSave:i=>t(`summary.prop.${r}.description`,i),className:"text-gray-600",multiline:!0})]})},n.title))})]})})}var du=new Map,ds=new WeakMap,Pp=0,zC=void 0;function BC(e){return e?(ds.has(e)||(Pp+=1,ds.set(e,Pp.toString())),ds.get(e)):"0"}function UC(e){return Object.keys(e).sort().filter(t=>e[t]!==void 0).map(t=>`${t}_${t==="root"?BC(e.root):e[t]}`).toString()}function HC(e){const t=UC(e);let n=du.get(t);if(!n){const r=new Map;let i;const s=new IntersectionObserver(o=>{o.forEach(a=>{var l;const u=a.isIntersecting&&i.some(c=>a.intersectionRatio>=c);e.trackVisibility&&typeof a.isVisible>"u"&&(a.isVisible=u),(l=r.get(a.target))==null||l.forEach(c=>{c(u,a)})})},e);i=s.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:s,elements:r},du.set(t,n)}return n}function WC(e,t,n={},r=zC){if(typeof window.IntersectionObserver>"u"&&r!==void 0){const l=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:typeof n.threshold=="number"?n.threshold:0,time:0,boundingClientRect:l,intersectionRect:l,rootBounds:l}),()=>{}}const{id:i,observer:s,elements:o}=HC(n),a=o.get(e)||[];return o.has(e)||o.set(e,a),a.push(t),s.observe(e),function(){a.splice(a.indexOf(t),1),a.length===0&&(o.delete(e),s.unobserve(e)),o.size===0&&(s.disconnect(),du.delete(i))}}function In({threshold:e,delay:t,trackVisibility:n,rootMargin:r,root:i,triggerOnce:s,skip:o,initialInView:a,fallbackInView:l,onChange:u}={}){var c;const[p,f]=w.useState(null),y=w.useRef(),[v,x]=w.useState({inView:!!a,entry:void 0});y.current=u,w.useEffect(()=>{if(o||!p)return;let g;return g=WC(p,(k,E)=>{x({inView:k,entry:E}),y.current&&y.current(k,E),E.isIntersecting&&s&&g&&(g(),g=void 0)},{root:i,rootMargin:r,threshold:e,trackVisibility:n,delay:t},l),()=>{g&&g()}},[Array.isArray(e)?e.toString():e,p,i,r,s,o,n,l,t]);const S=(c=v.entry)==null?void 0:c.target,m=w.useRef();!p&&S&&!s&&!o&&m.current!==S&&(m.current=S,x({inView:!!a,entry:void 0}));const h=[f,v.inView,v.entry];return h.ref=h[0],h.inView=h[1],h.entry=h[2],h}function po({onClick:e,className:t=""}){return d.jsxs("button",{onClick:e,className:`flex items-center gap-1 sm:gap-2 text-sm sm:text-base text-primary-600 hover:text-primary-700 font-medium transition-colors ${t}`,children:["View More",d.jsx(E1,{className:"w-3 h-3 sm:w-4 sm:h-4"})]})}function KC(){const[e,t]=In({triggerOnce:!0,threshold:.1}),[n,r]=w.useState(!1),[i,s]=w.useState(null),o=a=>{s(a),r(!0)};return d.jsxs("section",{id:"skills",ref:e,className:"py-32 bg-gradient-to-br from-white to-primary-50",children:[d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},className:"text-center mb-16",children:[d.jsx("h2",{className:"text-4xl font-bold gradient-text mb-4",children:"Skills & Expertise"}),d.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Comprehensive technical expertise spanning AI, cloud architecture, and software development"})]}),d.jsx("div",{className:"space-y-16",children:Aa.map((a,l)=>d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{delay:l*.2},className:"glass-effect rounded-xl p-8 card-hover border border-primary-100",children:[d.jsxs("div",{className:"flex justify-between items-center mb-8",children:[d.jsx("h3",{className:"text-2xl font-bold gradient-text",children:a.title}),d.jsx(po,{onClick:()=>o(l)})]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.skills.slice(0,6).map((u,c)=>d.jsxs(re.div,{initial:{opacity:0,scale:.9},animate:t?{opacity:1,scale:1}:{},transition:{delay:l*.2+c*.1},className:`flex items-center gap-4 p-4 bg-white/50 rounded-xl transition-all duration-300 
                             hover:bg-primary-50 hover:shadow-lg hover:-translate-y-1`,children:[d.jsx("div",{className:"p-3 bg-primary-100 rounded-lg",children:d.jsx(u.icon,{className:"w-6 h-6 text-primary-600"})}),d.jsx("span",{className:"text-gray-700 font-medium",children:u.name})]},u.name))})]},a.title))})]}),d.jsx(Bo,{isOpen:n,onClose:()=>r(!1),title:i!==null?Aa[i].title:"",children:d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i!==null&&Aa[i].skills.map(a=>d.jsxs("div",{className:"flex items-center gap-4 p-4 bg-primary-50 rounded-xl",children:[d.jsx("div",{className:"p-3 bg-primary-100 rounded-lg",children:d.jsx(a.icon,{className:"w-6 h-6 text-primary-600"})}),d.jsx("span",{className:"text-gray-700 font-medium",children:a.name})]},a.name))})})]})}const wt=[{title:"Entrepreneur In Residence (DXB2)",company:"Antler",location:"Dubai, UAE",period:"March 2025 - Present",products:"Early-stage venture building, Startup ecosystem development",achievements:["Leading innovation initiatives in the Dubai startup ecosystem","Mentoring early-stage founders on technology strategy and implementation","Developing scalable technology frameworks for portfolio companies","Collaborating with investors and entrepreneurs to identify market opportunities","Conducting technical due diligence for potential investments","Building strategic partnerships with key technology providers","Advising on AI integration and digital transformation strategies","Contributing to the growth of Dubai's technology innovation hub"]},{title:"Director of Engineering & Delivery",company:"Kastech Software Solutions Group",location:"Hyderabad, India",period:"February 2023 - February 2025",products:"AI-powered web/mobile applications, Enterprise Solutions",achievements:["Spearhead generative AI integration in web/mobile app development, reducing time-to-market by 30%","Build and lead high-performing engineering teams across continents, boosting productivity by 40%","Implement mentor-driven development programs, improving team retention by 25%","Optimize resource allocation, achieving 20% cost efficiency while scaling operations","Implemented AI-powered code review system, reducing defects by 30%","Drive end-to-end project lifecycles with 95% on-time delivery","Revolutionize QA with AI-driven testing, reducing bug rates by 30%","Drive digital transformation projects, resulting in 15% YoY revenue growth"]},{title:"Director of Engineering",company:"Infinity Learn",location:"Hyderabad, India",period:"March 2022 - February 2023",products:"UAM, CMS, QB, Assessments, Learner Activity, OMS",achievements:["Led the expansion of UAM user base from 10k to 1 million users","Ensured stability and scalability of QB and Assessments platforms","Re-engineered and re-architected existing solutions","Established comprehensive product development strategy","Generated innovative industry solutions through ideation and analysis","Led sprint planning, review meetings, and Agile development process","Collaborated with stakeholders for requirement scoping and design","Developed project plans and monitored milestones for timely delivery"]},{title:"Co-Founder, Head of Technology",company:"Trice Systems Private Limited",location:"Hyderabad, India",period:"February 2019 - February 2022",products:"TRICE (Consumer, Partner, Delivery Mobile Apps, Admin Panel)",achievements:["Established comprehensive product development strategy","Generated innovative industry solutions through analysis","Led Agile development process and sprint planning","Conducted gap analysis for client requirements","Managed multiple projects throughout entire lifecycle","Enforced best practices in engineering design","Collaborated with stakeholders for product development","Delivered value-engineered services on schedule"]},{title:"Co-Founder, Head of Technology",company:"YINSOL",location:"Hyderabad, India",period:"November 2013 - February 2019",products:"TALHIRE, My Video CV, TRICE, Hello Flyers, Clinic to Door & Talent On",achievements:["Successfully conceptualized and built two groundbreaking products","Filed two patents for innovative HR technology solutions","Led technology strategy team focusing on framework architecture","Managed multiple projects throughout entire lifecycle","Served as global leader in management consulting","Designed comprehensive solution approaches","Implemented cutting-edge technologies for clients","Ensured data quality and system integrity"]},{title:"Advisory System Analyst",company:"IBM",location:"Hyderabad, India",period:"December 2008 - November 2013",products:"Enterprise Solutions, Data Analytics Systems",achievements:["Coordinated multi-team collaborations reducing delays by 20%","Consolidated IT infrastructure improving service delivery by 25%","Launched comprehensive Agile management framework","Enhanced project delivery efficiency by 25%","Improved client satisfaction scores by 25%","Established KPIs for tracking project outcomes","Developed comprehensive staffing plans","Reduced resource allocation time by 15 hours weekly"]},{title:"Consultant",company:"UBS",location:"Hyderabad, India",period:"July 2006 - July 2008",products:"Financial Systems, Enterprise Solutions",achievements:["Coordinated multi-team collaborations reducing delays by 20%","Consolidated IT infrastructure improving service delivery by 25%","Launched Agile project management framework","Enhanced project delivery efficiency by 25%","Improved client satisfaction scores by 25%","Established performance metrics and KPIs","Developed comprehensive staffing plans","Led strategic brainstorming initiatives"]},{title:"Associate Technical Consultant",company:"Wipro Infotech",location:"Hyderabad, India",period:"August 2005 - June 2006",products:"Enterprise IT Solutions",achievements:["Coordinated multi-team collaborations reducing delays by 20%","Consolidated IT infrastructure improving service delivery by 25%","Implemented Agile project management framework","Enhanced project delivery efficiency by 25%","Improved client satisfaction scores by 25%","Established performance metrics","Developed staffing plans","Led process improvement initiatives"]},{title:"Sr Software Engineer",company:"Alpsoft Enterprises",location:"Hyderabad, India",period:"December 2002 - July 2005",products:"Enterprise Applications",achievements:["Coordinated multi-team collaborations reducing delays by 20%","Consolidated IT infrastructure improving service delivery by 25%","Implemented Agile project management practices","Enhanced project delivery efficiency by 25%","Improved client satisfaction scores by 25%","Established performance metrics","Optimized resource allocation","Led technical initiatives"]}];function GC(){const[e,t]=w.useState(null),[n,r]=w.useState(!1),[i,s]=w.useState(4),[o,a]=In({triggerOnce:!0,threshold:.1}),{getContent:l,updateContent:u}=Nr(),c=f=>{t(f),r(!0)},p=()=>{s(f=>Math.min(f+4,wt.length))};return d.jsxs("section",{id:"experience",className:"py-32 bg-gradient-to-br from-gray-50 to-white",children:[d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(re.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-center mb-16",children:[d.jsx("h2",{className:"text-4xl font-bold gradient-text mb-4",children:"Professional Experience"}),d.jsx(F,{content:l("experience.description","Two decades of leadership, innovation, and technical excellence across global organizations"),onSave:f=>u("experience.description",f),className:"text-xl text-gray-600 max-w-2xl mx-auto",label:"Section Description"})]}),d.jsx("div",{className:"space-y-12",ref:o,children:wt.slice(0,i).map((f,y)=>d.jsxs(re.div,{initial:{opacity:0,x:-20},animate:a?{opacity:1,x:0}:{},transition:{duration:.5,delay:y*.1},className:"glass-effect rounded-xl p-8 card-hover border border-primary-100",children:[d.jsxs("div",{className:"flex flex-col lg:flex-row justify-between mb-8",children:[d.jsxs("div",{className:"space-y-3",children:[d.jsx(F,{content:l(`experience.${y}.title`,f.title),onSave:v=>u(`experience.${y}.title`,v),className:"text-2xl font-bold gradient-text",label:"Job Title"}),d.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(Jd,{className:"w-5 h-5 text-primary-600"}),d.jsx(F,{content:l(`experience.${y}.company`,f.company),onSave:v=>u(`experience.${y}.company`,v),className:"text-primary-600 font-medium",label:"Company Name"})]}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx($l,{className:"w-5 h-5 text-gray-500"}),d.jsx(F,{content:l(`experience.${y}.location`,f.location),onSave:v=>u(`experience.${y}.location`,v),className:"text-gray-500",label:"Location"})]})]})]}),d.jsxs("div",{className:"flex items-center justify-between gap-4 mt-4 lg:mt-0",children:[d.jsxs("div",{className:"flex items-center gap-2 text-gray-500",children:[d.jsx(Dl,{className:"w-5 h-5"}),d.jsx(F,{content:l(`experience.${y}.period`,f.period),onSave:v=>u(`experience.${y}.period`,v),className:"text-gray-500",label:"Time Period"})]}),d.jsx(po,{onClick:()=>c(y)})]})]}),d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center gap-2 text-gray-600 bg-primary-50 p-4 rounded-lg",children:[d.jsx(Fl,{className:"w-5 h-5 text-primary-600 flex-shrink-0"}),d.jsx(F,{content:l(`experience.${y}.products`,f.products),onSave:v=>u(`experience.${y}.products`,v),className:"font-medium",label:"Products"})]}),d.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:f.achievements.slice(0,2).map((v,x)=>d.jsxs(re.div,{initial:{opacity:0,y:10},animate:a?{opacity:1,y:0}:{},transition:{duration:.3,delay:x*.1},className:"flex items-start gap-3 p-4 rounded-lg hover:bg-primary-50 transition-colors",children:[d.jsx(Rl,{className:"w-5 h-5 text-primary-600 mt-1 flex-shrink-0"}),d.jsx(F,{content:l(`experience.${y}.achievements.${x}`,v),onSave:S=>u(`experience.${y}.achievements.${x}`,S),className:"text-gray-600",label:`Achievement ${x+1}`})]},v))})]})]},f.title+f.company))}),i<wt.length&&d.jsx("div",{className:"mt-12 text-center",children:d.jsxs("button",{onClick:p,className:"button-primary inline-flex items-center gap-2",children:["Load More Experience",d.jsx(A1,{className:"w-4 h-4"})]})})]}),d.jsx(Bo,{isOpen:n,onClose:()=>r(!1),title:e!==null?wt[e].title:"",children:e!==null&&d.jsxs("div",{className:"space-y-8",children:[d.jsxs("div",{className:"flex flex-wrap gap-4",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(Jd,{className:"w-5 h-5 text-primary-600"}),d.jsx(F,{content:l(`experience.${e}.company`,wt[e].company),onSave:f=>u(`experience.${e}.company`,f),className:"text-primary-600 font-medium",label:"Company Name"})]}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx($l,{className:"w-5 h-5 text-gray-500"}),d.jsx(F,{content:l(`experience.${e}.location`,wt[e].location),onSave:f=>u(`experience.${e}.location`,f),className:"text-gray-500",label:"Location"})]}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(Dl,{className:"w-5 h-5 text-gray-500"}),d.jsx(F,{content:l(`experience.${e}.period`,wt[e].period),onSave:f=>u(`experience.${e}.period`,f),className:"text-gray-500",label:"Time Period"})]})]}),d.jsxs("div",{className:"bg-primary-50 p-6 rounded-xl",children:[d.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Products & Systems"}),d.jsxs("div",{className:"flex items-start gap-3",children:[d.jsx(Fl,{className:"w-5 h-5 text-primary-600 mt-1"}),d.jsx(F,{content:l(`experience.${e}.products`,wt[e].products),onSave:f=>u(`experience.${e}.products`,f),className:"text-gray-700",label:"Products"})]})]}),d.jsxs("div",{children:[d.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Key Achievements"}),d.jsx("div",{className:"grid gap-4",children:wt[e].achievements.map((f,y)=>d.jsxs("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[d.jsx(Rl,{className:"w-5 h-5 text-primary-600 mt-1"}),d.jsx(F,{content:l(`experience.${e}.achievements.${y}`,f),onSave:v=>u(`experience.${e}.achievements.${y}`,v),className:"text-gray-700",label:`Achievement ${y+1}`})]},f))})]})]})})]})}const YC=[{title:"System and Method for Determining Genuineness Corresponding to a Work Experience of a Job Seeker",icon:N1,description:"An innovative system that leverages AI and machine learning to verify and validate work experience claims, enhancing recruitment accuracy and reducing fraudulent applications."},{title:"System and Method for Evaluating a Video Resume and Conducting a Video Based Interview of a Job Seeker",icon:Ao,description:"A comprehensive platform for conducting and analyzing video-based interviews, incorporating advanced analytics and AI-driven assessment tools."}];function QC(){const[e,t]=In({triggerOnce:!0,threshold:.1});return d.jsx("section",{id:"patents",ref:e,className:"section-padding bg-gradient-to-br from-primary-50 via-white to-accent-50",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},className:"text-center mb-12",children:[d.jsx("h2",{className:"text-4xl font-bold gradient-text mb-4",children:"Patents"}),d.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Innovative solutions in HR technology and recruitment, transforming how organizations hire and evaluate talent"})]}),d.jsx("div",{className:"grid md:grid-cols-2 gap-8",children:YC.map((n,r)=>{const i=n.icon;return d.jsx(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{delay:r*.2},className:"glass-effect rounded-xl p-8 card-hover border border-primary-100",children:d.jsxs("div",{className:"flex items-start gap-4",children:[d.jsx("div",{className:"p-3 bg-primary-100 rounded-lg",children:d.jsx(i,{className:"w-6 h-6 text-primary-600"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:n.title}),d.jsx("p",{className:"text-gray-600",children:n.description})]})]})},r)})})]})})}function XC(){const[e,t]=In({triggerOnce:!0,threshold:.1}),[n,r]=w.useState(!1),[i,s]=w.useState(null),o=a=>{s(a),r(!0)};return d.jsx("section",{id:"education",className:"section-padding bg-gradient-to-br from-primary-50 via-white to-accent-50",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},className:"text-center mb-12",children:[d.jsx("h2",{className:"text-4xl font-bold gradient-text mb-4",children:"Education & Certifications"}),d.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Academic excellence, professional certifications, and technical expertise"})]}),d.jsxs("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[d.jsx("div",{className:"glass-effect rounded-xl p-8 card-hover",children:d.jsxs("div",{className:"flex items-start gap-4",children:[d.jsx("div",{className:"p-3 bg-primary-100 rounded-lg",children:d.jsx(M1,{className:"w-6 h-6 text-primary-600"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Education"}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("p",{className:"text-primary-600 font-medium",children:"B.Sc. Computer Science"}),d.jsx("p",{className:"text-gray-600",children:"Andhra University (1997 - 2000)"})]})]})]})}),d.jsx("div",{className:"glass-effect rounded-xl p-8 card-hover",children:d.jsxs("div",{className:"flex items-start gap-4",children:[d.jsx("div",{className:"p-3 bg-primary-100 rounded-lg",children:d.jsx(Js,{className:"w-6 h-6 text-primary-600"})}),d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex justify-between items-center mb-4",children:[d.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Languages"}),d.jsx(po,{onClick:()=>o("languages")})]}),d.jsx("div",{className:"space-y-3",children:yp.filter(a=>a.visible).map(a=>d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-700",children:a.name}),d.jsx("span",{className:"text-gray-500 text-sm",children:a.level})]},a.name))})]})]})})]}),d.jsx("div",{className:"glass-effect rounded-xl p-8 card-hover",children:d.jsxs("div",{className:"flex items-start gap-4",children:[d.jsx("div",{className:"p-3 bg-primary-100 rounded-lg",children:d.jsx(jn,{className:"w-6 h-6 text-primary-600"})}),d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex justify-between items-center mb-6",children:[d.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Professional Certifications"}),d.jsx(po,{onClick:()=>o("certifications")})]}),d.jsx("div",{className:"grid sm:grid-cols-2 gap-6",children:ru.slice(0,2).map(a=>d.jsxs("div",{className:"flex items-start gap-3",children:[d.jsx(a.icon,{className:"w-5 h-5 text-primary-600 mt-1"}),d.jsxs("div",{children:[d.jsx("p",{className:"font-medium text-gray-900",children:a.name}),d.jsx("p",{className:"text-gray-600 text-sm",children:a.issuer})]})]},a.name))})]})]})}),d.jsx(Bo,{isOpen:n,onClose:()=>r(!1),title:i==="certifications"?"Professional Certifications":"Language Proficiency",children:i==="certifications"?d.jsx("div",{className:"grid gap-6",children:ru.map(a=>d.jsxs("div",{className:"flex items-start gap-4 p-4 bg-gray-50 rounded-lg",children:[d.jsx(a.icon,{className:"w-6 h-6 text-primary-600"}),d.jsxs("div",{children:[d.jsx("p",{className:"font-medium text-gray-900",children:a.name}),d.jsx("p",{className:"text-gray-600",children:a.issuer})]})]},a.name))}):d.jsx("div",{className:"grid gap-6",children:yp.filter(a=>a.visible).map(a=>d.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[d.jsx("span",{className:"font-medium text-gray-900",children:a.name}),d.jsx("span",{className:"text-primary-600",children:a.level})]},a.name))})})]})})}function qC(){return d.jsx("footer",{className:"bg-gray-900 text-gray-300",children:d.jsx("div",{className:"max-w-6xl mx-auto px-4 py-6 sm:py-8",children:d.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4",children:[d.jsxs("p",{className:"text-sm sm:text-base text-center sm:text-left",children:["© ",new Date().getFullYear()," Surendra Ganne. All rights reserved."]}),d.jsxs("div",{className:"flex gap-4",children:[d.jsx("a",{href:"https://github.com/surenganne",className:"hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:d.jsx(_l,{className:"w-5 h-5"})}),d.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-white transition-colors",children:d.jsx(eo,{className:"w-5 h-5"})}),d.jsx("a",{href:"https://www.linkedin.com/in/surenganne/",className:"hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:d.jsx(Ol,{className:"w-5 h-5"})})]})]})})})}const fs={totalPeopleManaged:"250+",totalInterviews:"1500+",totalHires:"350+",performanceReviews:"450+",projectsDelivered:"60+",patentsFiled:2,teamRetentionRate:"92%",avgProductivityIncrease:"40%"},ZC=[{company:"Kastech Software Solutions Group",period:"2023-Present",metrics:{teamSize:"50",interviews:"150",hires:"45",performanceReviews:"70",kras:["Drive AI integration across products","Scale engineering teams globally","Optimize resource allocation","Improve code quality metrics","Enhance delivery timelines"]}},{company:"Infinity Learn",period:"2022-2023",metrics:{teamSize:"50",interviews:"150",hires:"30",performanceReviews:"70",kras:["Scale UAM platform","Enhance system stability","Improve architecture","Drive agile transformation","Optimize development processes"]}},{company:"Trice Systems",period:"2019-2022",metrics:{teamSize:"12",interviews:"100",hires:"9",performanceReviews:"100",kras:["Lead product development","Implement microservices","Drive mobile app development","Enhance team capabilities","Improve delivery processes"]}}],JC=({icon:e,label:t,value:n,companyKey:r,statKey:i,className:s=""})=>{const{getContent:o,updateContent:a}=Nr(),l=o(`metrics.company.${r}.stats.${i}`,n.toString());return d.jsxs("div",{className:`relative overflow-hidden bg-white rounded-xl shadow-sm hover:shadow-md 
                    transition-all duration-300 group ${s}`,children:[d.jsx("div",{className:`absolute top-0 right-0 w-32 h-32 -mr-16 -mt-16 bg-primary-100 rounded-full opacity-10 
                    group-hover:scale-110 transition-transform duration-500`}),d.jsxs("div",{className:"relative p-6",children:[d.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[d.jsx("div",{className:"p-2 bg-primary-50 rounded-lg group-hover:bg-primary-100 transition-colors",children:d.jsx(e,{className:"w-5 h-5 text-primary-600"})}),d.jsx(F,{content:o(`metrics.company.${r}.stats.${i}.label`,t),onSave:u=>a(`metrics.company.${r}.stats.${i}.label`,u),className:"text-sm font-medium text-gray-600",label:`${t} Label`})]}),d.jsxs("div",{className:"text-3xl font-bold text-primary-600",children:[d.jsx(F,{content:l,onSave:u=>a(`metrics.company.${r}.stats.${i}`,u),className:"inline-block",label:`${t} Value`}),d.jsx("span",{className:"text-primary-400",children:"+"})]})]})]})};function eP({metrics:e,isOpen:t,onToggle:n,delay:r}){const{getContent:i,updateContent:s}=Nr(),o=[{icon:Ao,label:"Team Size",key:"teamSize",value:e.metrics.teamSize},{icon:Wm,label:"Interviews",key:"interviews",value:e.metrics.interviews},{icon:_1,label:"Hires",key:"hires",value:e.metrics.hires},{icon:Hm,label:"Reviews",key:"reviews",value:e.metrics.performanceReviews}];return d.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:r},className:"bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300",children:[d.jsxs("div",{className:"flex justify-between items-center p-6 cursor-pointer hover:bg-gray-50 transition-colors",onClick:n,children:[d.jsxs("div",{className:"flex items-center gap-6",children:[d.jsx("div",{className:"p-4 bg-primary-50 rounded-2xl",children:d.jsx(Il,{className:"w-8 h-8 text-primary-600"})}),d.jsxs("div",{children:[d.jsx(F,{content:i(`metrics.company.${e.company}.name`,e.company),onSave:a=>s(`metrics.company.${e.company}.name`,a),className:"text-2xl font-bold text-gray-900",label:"Company Name"}),d.jsx(F,{content:i(`metrics.company.${e.company}.period`,e.period),onSave:a=>s(`metrics.company.${e.company}.period`,a),className:"text-base text-gray-600 mt-1",label:"Period"})]})]}),d.jsx(re.div,{animate:{rotate:t?180:0},transition:{duration:.3},className:"w-10 h-10 flex items-center justify-center rounded-full bg-primary-50",children:d.jsx(Um,{className:"w-6 h-6 text-primary-600"})})]}),d.jsx(re.div,{initial:!1,animate:{height:t?"auto":0,opacity:t?1:0},transition:{duration:.3},className:"overflow-hidden",children:d.jsxs("div",{className:"p-6 space-y-8 border-t border-gray-100",children:[d.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:o.map(a=>d.jsx(JC,{icon:a.icon,label:a.label,value:a.value,companyKey:e.company,statKey:a.key},a.key))}),d.jsxs("div",{className:"bg-gray-50 rounded-xl p-6",children:[d.jsx("h4",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Key Responsibility Areas"}),d.jsx("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-4",children:e.metrics.kras.map((a,l)=>d.jsxs(re.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:l*.1},className:`flex items-center gap-4 p-4 bg-white rounded-xl shadow-sm hover:shadow-md 
                           transition-all duration-300 hover:-translate-y-1 group`,children:[d.jsx("div",{className:"p-2 bg-primary-50 rounded-lg group-hover:bg-primary-100 transition-colors",children:d.jsx(Il,{className:"w-5 h-5 text-primary-600"})}),d.jsx(F,{content:i(`metrics.company.${e.company}.kras.${l}`,a),onSave:u=>s(`metrics.company.${e.company}.kras.${l}`,u),className:"text-gray-700 group-hover:text-gray-900 transition-colors",label:`KRA ${l+1}`})]},l))})]})]})})]})}const tP=({icon:e,label:t,value:n,delay:r=0})=>{const[i,s]=In({triggerOnce:!0,threshold:.1});return d.jsxs(re.div,{ref:i,initial:{opacity:0,y:20},animate:s?{opacity:1,y:0}:{},transition:{delay:r},className:`relative group overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl 
                 transition-all duration-300 transform hover:-translate-y-1`,children:[d.jsx("div",{className:`absolute top-0 right-0 w-32 h-32 -mr-16 -mt-16 bg-primary-100 rounded-full opacity-10 
                    group-hover:scale-110 transition-transform duration-500`}),d.jsxs("div",{className:"relative p-6",children:[d.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[d.jsx("div",{className:"p-3 bg-primary-50 rounded-xl group-hover:bg-primary-100 transition-colors",children:d.jsx(e,{className:"w-6 h-6 text-primary-600"})}),d.jsx("span",{className:"text-sm font-medium text-gray-600",children:t})]}),d.jsxs("div",{className:"flex items-baseline justify-between",children:[d.jsx("div",{className:"text-3xl font-bold text-gray-900",children:n}),d.jsx(Bm,{className:"w-5 h-5 text-primary-600 opacity-0 group-hover:opacity-100 transition-opacity"})]})]})]})};function nP(){const[e,t]=In({triggerOnce:!0,threshold:.1}),[n,r]=w.useState(null),i=s=>{r(n===s?null:s)};return d.jsx("section",{id:"metrics",ref:e,className:"py-32 bg-gradient-to-br from-primary-50 via-white to-accent-50",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},className:"text-center mb-16",children:[d.jsx("h2",{className:"text-4xl font-bold gradient-text mb-4",children:"Impact & Achievements"}),d.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Measurable results and key performance indicators across leadership roles"})]}),d.jsxs("div",{className:"mb-16",children:[d.jsx("h3",{className:"text-2xl font-bold gradient-text mb-8",children:"Overall Impact"}),d.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[{icon:Ao,label:"People Managed",value:fs.totalPeopleManaged},{icon:Wm,label:"Total Interviews",value:fs.totalInterviews},{icon:Hm,label:"Performance Reviews",value:fs.performanceReviews},{icon:Fl,label:"Projects Delivered",value:fs.projectsDelivered}].map((s,o)=>d.jsx(tP,{icon:s.icon,label:s.label,value:s.value,delay:o*.1},s.label))})]}),d.jsxs("div",{className:"space-y-8",children:[d.jsx("h3",{className:"text-2xl font-bold gradient-text mb-8",children:"Company-wise Metrics"}),ZC.map((s,o)=>d.jsx(eP,{metrics:s,isOpen:n===s.company,onToggle:()=>i(s.company),delay:o*.2},s.company))]})]})})}const rP=[{company:"Kastech Software Solutions Group",challenges:[{title:"Rapid AI Integration",description:"Challenge: Integrating AI capabilities into existing products while maintaining stability.",solution:"Implemented phased approach with parallel systems, allowing gradual transition and thorough testing.",outcome:"Successfully integrated AI features with zero downtime and 30% performance improvement."},{title:"Global Team Coordination",description:"Challenge: Managing teams across multiple time zones and cultural contexts.",solution:"Established structured communication protocols and implemented async-first workflows.",outcome:"Improved team collaboration and reduced meeting overhead by 40%."}]},{company:"Infinity Learn",challenges:[{title:"Platform Scalability",description:"Challenge: Scaling user base from 10k to 1M+ users while maintaining performance.",solution:"Implemented microservices architecture and optimized database queries.",outcome:"Achieved 99.9% uptime during 100x user growth."},{title:"Legacy System Migration",description:"Challenge: Modernizing legacy systems without disrupting operations.",solution:"Developed staged migration plan with fallback mechanisms.",outcome:"Completed migration with zero data loss and minimal downtime."}]},{company:"Trice Systems",challenges:[{title:"Product Market Fit",description:"Challenge: Pivoting product strategy based on market feedback.",solution:"Implemented rapid prototyping and continuous user feedback loops.",outcome:"Achieved product-market fit within 6 months."},{title:"Resource Optimization",description:"Challenge: Managing resource constraints while maintaining quality.",solution:"Introduced automated testing and CI/CD pipelines.",outcome:"Reduced development cycle time by 40%."}]}];function iP(){const[e,t]=In({triggerOnce:!0,threshold:.1}),[n,r]=w.useState(null),{getContent:i,updateContent:s}=Nr();return d.jsx("section",{id:"challenges",ref:e,className:"py-32 bg-gradient-to-br from-primary-50 via-white to-accent-50",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},className:"text-center mb-16",children:[d.jsx("h2",{className:"text-4xl font-bold gradient-text mb-4",children:"Challenges & Solutions"}),d.jsx(F,{content:i("challenges.description","Key challenges faced and innovative solutions implemented across different roles"),onSave:o=>s("challenges.description",o),className:"text-xl text-gray-600 max-w-2xl mx-auto",label:"Section Description"})]}),d.jsx("div",{className:"space-y-8",children:rP.map((o,a)=>d.jsxs(re.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{delay:a*.2},className:"bg-white rounded-2xl shadow-lg overflow-hidden",children:[d.jsx("div",{className:"p-6 border-b border-gray-100",children:d.jsx(F,{content:i(`challenges.${a}.company`,o.company),onSave:l=>s(`challenges.${a}.company`,l),className:"text-2xl font-bold gradient-text",label:"Company Name"})}),d.jsx("div",{className:"p-6 space-y-4",children:o.challenges.map((l,u)=>d.jsxs("div",{className:"bg-gray-50 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-md",children:[d.jsxs("div",{className:"flex justify-between items-center p-4 cursor-pointer hover:bg-gray-100 transition-colors",onClick:()=>r(n===l.title?null:l.title),children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("div",{className:"p-2 bg-primary-100 rounded-lg",children:d.jsx(zm,{className:"w-5 h-5 text-primary-600"})}),d.jsx(F,{content:i(`challenges.${a}.${u}.title`,l.title),onSave:c=>s(`challenges.${a}.${u}.title`,c),className:"font-semibold text-gray-900",label:"Challenge Title"})]}),d.jsx(re.div,{animate:{rotate:n===l.title?180:0},transition:{duration:.3},children:d.jsx(Um,{className:"w-5 h-5 text-gray-500"})})]}),n===l.title&&d.jsxs(re.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"px-4 pb-4 space-y-4",children:[d.jsx("div",{className:"p-4 bg-white rounded-xl border border-gray-100",children:d.jsx(F,{content:i(`challenges.${a}.${u}.description`,l.description),onSave:c=>s(`challenges.${a}.${u}.description`,c),className:"text-gray-700",label:"Challenge Description",multiline:!0})}),d.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[d.jsxs("div",{className:"p-4 bg-green-50 rounded-xl",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[d.jsx(Rl,{className:"w-5 h-5 text-green-600"}),d.jsx("h4",{className:"font-medium text-green-900",children:"Solution"})]}),d.jsx(F,{content:i(`challenges.${a}.${u}.solution`,l.solution),onSave:c=>s(`challenges.${a}.${u}.solution`,c),className:"text-green-800",label:"Solution",multiline:!0})]}),d.jsxs("div",{className:"p-4 bg-blue-50 rounded-xl",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[d.jsx(Bm,{className:"w-5 h-5 text-blue-600"}),d.jsx("h4",{className:"font-medium text-blue-900",children:"Outcome"})]}),d.jsx(F,{content:i(`challenges.${a}.${u}.outcome`,l.outcome),onSave:c=>s(`challenges.${a}.${u}.outcome`,c),className:"text-blue-800",label:"Outcome",multiline:!0})]})]})]})]},l.title))})]},o.company))})]})})}function sP(){return d.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-accent-50",children:[d.jsx(O1,{}),d.jsxs("main",{children:[d.jsx($C,{}),d.jsx(IC,{}),d.jsx(KC,{}),d.jsx(GC,{}),d.jsx(nP,{}),d.jsx(iP,{}),d.jsx(QC,{}),d.jsx(XC,{})]}),d.jsx(qC,{})]})}Im(document.getElementById("root")).render(d.jsx(w.StrictMode,{children:d.jsx(sP,{})}));
