import React from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Summary from './components/Summary';
import Skills from './components/Skills';
import Experience from './components/Experience';
import Patents from './components/Patents';
import Education from './components/Education';
import Footer from './components/Footer';
import MetricsDashboard from './components/MetricsDashboard';
import Challenges from './components/Challenges';

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50">
      <Header />
      <main>
        <Hero />
        <Summary />
        <Skills />
        <Experience />
        <MetricsDashboard />
        <Challenges />
        <Patents />
        <Education />
      </main>
      <Footer />
    </div>
  );
}

export default App;