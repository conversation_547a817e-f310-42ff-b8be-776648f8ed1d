import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { AlertTriangle, CheckCircle, ChevronDown, ArrowUpRight } from 'lucide-react';
import { challenges } from '../data/challenges';
import EditableContent from './EditableContent';
import { useEditStore } from '../store/editStore';

export default function Challenges() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const [expandedChallenge, setExpandedChallenge] = useState<string | null>(null);
  const { getContent, updateContent } = useEditStore();

  return (
    <section id="challenges" ref={ref} className="py-32 bg-gradient-to-br from-primary-50 via-white to-accent-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold gradient-text mb-4">Challenges & Solutions</h2>
          <EditableContent
            content={getContent('challenges.description', 'Key challenges faced and innovative solutions implemented across different roles')}
            onSave={(content) => updateContent('challenges.description', content)}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
            label="Section Description"
          />
        </motion.div>

        <div className="space-y-8">
          {challenges.map((companyData, companyIndex) => (
            <motion.div
              key={companyData.company}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: companyIndex * 0.2 }}
              className="bg-white rounded-2xl shadow-lg overflow-hidden"
            >
              <div className="p-6 border-b border-gray-100">
                <EditableContent
                  content={getContent(`challenges.${companyIndex}.company`, companyData.company)}
                  onSave={(content) => updateContent(`challenges.${companyIndex}.company`, content)}
                  className="text-2xl font-bold gradient-text"
                  label="Company Name"
                />
              </div>
              
              <div className="p-6 space-y-4">
                {companyData.challenges.map((challenge, challengeIndex) => (
                  <div
                    key={challenge.title}
                    className="bg-gray-50 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-md"
                  >
                    <div
                      className="flex justify-between items-center p-4 cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => setExpandedChallenge(
                        expandedChallenge === challenge.title ? null : challenge.title
                      )}
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary-100 rounded-lg">
                          <AlertTriangle className="w-5 h-5 text-primary-600" />
                        </div>
                        <EditableContent
                          content={getContent(`challenges.${companyIndex}.${challengeIndex}.title`, challenge.title)}
                          onSave={(content) => updateContent(`challenges.${companyIndex}.${challengeIndex}.title`, content)}
                          className="font-semibold text-gray-900"
                          label="Challenge Title"
                        />
                      </div>
                      <motion.div
                        animate={{ rotate: expandedChallenge === challenge.title ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      </motion.div>
                    </div>
                    
                    {expandedChallenge === challenge.title && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="px-4 pb-4 space-y-4"
                      >
                        <div className="p-4 bg-white rounded-xl border border-gray-100">
                          <EditableContent
                            content={getContent(`challenges.${companyIndex}.${challengeIndex}.description`, challenge.description)}
                            onSave={(content) => updateContent(`challenges.${companyIndex}.${challengeIndex}.description`, content)}
                            className="text-gray-700"
                            label="Challenge Description"
                            multiline
                          />
                        </div>
                        
                        <div className="grid md:grid-cols-2 gap-4">
                          <div className="p-4 bg-green-50 rounded-xl">
                            <div className="flex items-center gap-2 mb-2">
                              <CheckCircle className="w-5 h-5 text-green-600" />
                              <h4 className="font-medium text-green-900">Solution</h4>
                            </div>
                            <EditableContent
                              content={getContent(`challenges.${companyIndex}.${challengeIndex}.solution`, challenge.solution)}
                              onSave={(content) => updateContent(`challenges.${companyIndex}.${challengeIndex}.solution`, content)}
                              className="text-green-800"
                              label="Solution"
                              multiline
                            />
                          </div>
                          
                          <div className="p-4 bg-blue-50 rounded-xl">
                            <div className="flex items-center gap-2 mb-2">
                              <ArrowUpRight className="w-5 h-5 text-blue-600" />
                              <h4 className="font-medium text-blue-900">Outcome</h4>
                            </div>
                            <EditableContent
                              content={getContent(`challenges.${companyIndex}.${challengeIndex}.outcome`, challenge.outcome)}
                              onSave={(content) => updateContent(`challenges.${companyIndex}.${challengeIndex}.outcome`, content)}
                              className="text-blue-800"
                              label="Outcome"
                              multiline
                            />
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}