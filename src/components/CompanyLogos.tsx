import React from 'react';

const CompanyLogos: React.FC = () => {
  const logos = [
    { name: 'Antler', imgSrc: '/images/antler.svg' },
    { name: 'Infinity Learn', imgSrc: '/images/infinity-learn.webp' },
    { name: 'IBM', imgSrc: '/images/IBM.png' },
    { name: 'UBS', imgSrc: '/images/UBS_Logo.png' },
    { name: 'Wipro', imgSrc: '/images/wipro.png' },
    { name: 'Trice', imgSrc: '/images/trice.jpeg' }
  ];

  return (
    <div className="flex flex-wrap justify-center gap-6 mb-8">
      {logos.map((logo, index) => (
        <div key={index} className="glass-effect rounded-xl p-3 flex flex-col items-center">
          <div className="w-16 h-16 mb-2 flex items-center justify-center">
            <img 
              src={logo.imgSrc} 
              alt={`${logo.name} logo`} 
              className="max-w-full max-h-full object-contain" 
            />
          </div>
          <span className="text-xs text-gray-700">{logo.name}</span>
        </div>
      ))}
    </div>
  );
};

export default CompanyLogos;
