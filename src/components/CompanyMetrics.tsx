import React from 'react';
import { motion } from 'framer-motion';
import { Target, ChevronDown, Users, UserCheck, UserPlus, ClipboardCheck } from 'lucide-react';
import EditableContent from './EditableContent';
import { useEditStore } from '../store/editStore';
import CountUp from 'react-countup';

interface CompanyMetricsProps {
  metrics: any;
  isOpen: boolean;
  onToggle: () => void;
  delay: number;
}

const StatCard = ({ 
  icon: Icon, 
  label, 
  value, 
  companyKey,
  statKey,
  className = '' 
}: {
  icon: any;
  label: string;
  value: number;
  companyKey: string;
  statKey: string;
  className?: string;
}) => {
  const { getContent, updateContent } = useEditStore();
  const storedValue = getContent(`metrics.company.${companyKey}.stats.${statKey}`, value.toString());
  
  return (
    <div className={`relative overflow-hidden bg-white rounded-xl shadow-sm hover:shadow-md 
                    transition-all duration-300 group ${className}`}>
      <div className="absolute top-0 right-0 w-32 h-32 -mr-16 -mt-16 bg-primary-100 rounded-full opacity-10 
                    group-hover:scale-110 transition-transform duration-500" />
      <div className="relative p-6">
        <div className="flex items-center gap-3 mb-3">
          <div className="p-2 bg-primary-50 rounded-lg group-hover:bg-primary-100 transition-colors">
            <Icon className="w-5 h-5 text-primary-600" />
          </div>
          <EditableContent
            content={getContent(`metrics.company.${companyKey}.stats.${statKey}.label`, label)}
            onSave={(content) => updateContent(`metrics.company.${companyKey}.stats.${statKey}.label`, content)}
            className="text-sm font-medium text-gray-600"
            label={`${label} Label`}
          />
        </div>
        <div className="text-3xl font-bold text-primary-600">
          <EditableContent
            content={storedValue}
            onSave={(content) => updateContent(`metrics.company.${companyKey}.stats.${statKey}`, content)}
            className="inline-block"
            label={`${label} Value`}
          />
          <span className="text-primary-400">+</span>
        </div>
      </div>
    </div>
  );
};

export default function CompanyMetrics({ metrics, isOpen, onToggle, delay }: CompanyMetricsProps) {
  const { getContent, updateContent } = useEditStore();

  const statCards = [
    { icon: Users, label: 'Team Size', key: 'teamSize', value: metrics.metrics.teamSize },
    { icon: UserCheck, label: 'Interviews', key: 'interviews', value: metrics.metrics.interviews },
    { icon: UserPlus, label: 'Hires', key: 'hires', value: metrics.metrics.hires },
    { icon: ClipboardCheck, label: 'Reviews', key: 'reviews', value: metrics.metrics.performanceReviews }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
      className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
    >
      <div 
        className="flex justify-between items-center p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center gap-6">
          <div className="p-4 bg-primary-50 rounded-2xl">
            <Target className="w-8 h-8 text-primary-600" />
          </div>
          <div>
            <EditableContent
              content={getContent(`metrics.company.${metrics.company}.name`, metrics.company)}
              onSave={(content) => updateContent(`metrics.company.${metrics.company}.name`, content)}
              className="text-2xl font-bold text-gray-900"
              label="Company Name"
            />
            <EditableContent
              content={getContent(`metrics.company.${metrics.company}.period`, metrics.period)}
              onSave={(content) => updateContent(`metrics.company.${metrics.company}.period`, content)}
              className="text-base text-gray-600 mt-1"
              label="Period"
            />
          </div>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="w-10 h-10 flex items-center justify-center rounded-full bg-primary-50"
        >
          <ChevronDown className="w-6 h-6 text-primary-600" />
        </motion.div>
      </div>

      <motion.div
        initial={false}
        animate={{ 
          height: isOpen ? 'auto' : 0,
          opacity: isOpen ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="p-6 space-y-8 border-t border-gray-100">
          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {statCards.map((stat) => (
              <StatCard
                key={stat.key}
                icon={stat.icon}
                label={stat.label}
                value={stat.value}
                companyKey={metrics.company}
                statKey={stat.key}
              />
            ))}
          </div>

          {/* KRAs */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h4 className="text-xl font-semibold text-gray-900 mb-6">Key Responsibility Areas</h4>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {metrics.metrics.kras.map((kra: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center gap-4 p-4 bg-white rounded-xl shadow-sm hover:shadow-md 
                           transition-all duration-300 hover:-translate-y-1 group"
                >
                  <div className="p-2 bg-primary-50 rounded-lg group-hover:bg-primary-100 transition-colors">
                    <Target className="w-5 h-5 text-primary-600" />
                  </div>
                  <EditableContent
                    content={getContent(`metrics.company.${metrics.company}.kras.${index}`, kra)}
                    onSave={(content) => updateContent(`metrics.company.${metrics.company}.kras.${index}`, content)}
                    className="text-gray-700 group-hover:text-gray-900 transition-colors"
                    label={`KRA ${index + 1}`}
                  />
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}