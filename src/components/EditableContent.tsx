import React, { useState } from 'react';

interface EditableContentProps {
  content: string;
  className?: string;
  onSave?: (content: string) => void;
  multiline?: boolean;
}

export default function EditableContent({ 
  content, 
  className = '',
  onSave,
  multiline = false
}: EditableContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(content);

  const handleEdit = () => {
    setEditedContent(content);
    setIsEditing(true);
  };

  const handleSave = () => {
    if (onSave) {
      onSave(editedContent);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !multiline) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className="relative">
        {multiline ? (
          <textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            onKeyDown={handleKeyDown}
            className={`w-full p-2 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 ${className}`}
            rows={3}
            autoFocus
          />
        ) : (
          <input
            type="text"
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            onKeyDown={handleKeyDown}
            className={`w-full p-2 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 ${className}`}
            autoFocus
          />
        )}
        <div className="flex mt-2 gap-2 justify-end">
          <button
            onClick={handleCancel}
            className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-3 py-1 text-sm bg-primary-500 text-white hover:bg-primary-600 rounded transition-colors"
          >
            Save
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`${className} ${onSave ? 'cursor-pointer hover:bg-primary-50 hover:px-2 rounded transition-all' : ''}`}
      onClick={onSave ? handleEdit : undefined}
    >
      {content}
    </div>
  );
}