import React, { useState } from 'react';
import { GraduationCap, Award, Code, Terminal, Brain, Globe } from 'lucide-react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Modal from './Modal';
import ViewMoreButton from './ViewMoreButton';
import { certifications, languages } from '../data/skills';

export default function Education() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<'certifications' | 'languages' | null>(null);

  const openModal = (content: 'certifications' | 'languages') => {
    setModalContent(content);
    setIsModalOpen(true);
  };

  return (
    <section id="education" className="section-padding bg-gradient-to-br from-primary-50 via-white to-accent-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold gradient-text mb-4">Education & Certifications</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Academic excellence, professional certifications, and technical expertise
          </p>
        </motion.div>
        
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="glass-effect rounded-xl p-8 card-hover">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-primary-100 rounded-lg">
                <GraduationCap className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Education</h3>
                <div className="space-y-2">
                  <p className="text-primary-600 font-medium">B.Sc. Computer Science</p>
                  <p className="text-gray-600">Andhra University (1997 - 2000)</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="glass-effect rounded-xl p-8 card-hover">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-primary-100 rounded-lg">
                <Globe className="w-6 h-6 text-primary-600" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900">Languages</h3>
                  <ViewMoreButton onClick={() => openModal('languages')} />
                </div>
                <div className="space-y-3">
                  {languages.filter(lang => lang.visible).map((lang) => (
                    <div key={lang.name} className="flex items-center justify-between">
                      <span className="text-gray-700">{lang.name}</span>
                      <span className="text-gray-500 text-sm">{lang.level}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="glass-effect rounded-xl p-8 card-hover">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-primary-100 rounded-lg">
              <Award className="w-6 h-6 text-primary-600" />
            </div>
            <div className="flex-1">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-gray-900">Professional Certifications</h3>
                <ViewMoreButton onClick={() => openModal('certifications')} />
              </div>
              <div className="grid sm:grid-cols-2 gap-6">
                {certifications.slice(0, 2).map((cert) => (
                  <div key={cert.name} className="flex items-start gap-3">
                    <cert.icon className="w-5 h-5 text-primary-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900">{cert.name}</p>
                      <p className="text-gray-600 text-sm">{cert.issuer}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={modalContent === 'certifications' ? 'Professional Certifications' : 'Language Proficiency'}
        >
          {modalContent === 'certifications' ? (
            <div className="grid gap-6">
              {certifications.map((cert) => (
                <div key={cert.name} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                  <cert.icon className="w-6 h-6 text-primary-600" />
                  <div>
                    <p className="font-medium text-gray-900">{cert.name}</p>
                    <p className="text-gray-600">{cert.issuer}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid gap-6">
              {languages.filter(lang => lang.visible).map((lang) => (
                <div key={lang.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-900">{lang.name}</span>
                  <span className="text-primary-600">{lang.level}</span>
                </div>
              ))}
            </div>
          )}
        </Modal>
      </div>
    </section>
  );
}