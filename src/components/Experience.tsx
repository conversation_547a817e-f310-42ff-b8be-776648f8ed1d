import React, { useState } from 'react';
import { Calendar, Package, CheckCircle, Building, MapPin, Link } from 'lucide-react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { experiences } from '../data/experiences';
import ViewMoreButton from './ViewMoreButton';
import Modal from './Modal';
import EditableContent from './EditableContent';
import { useEditStore } from '../store/editStore';

export default function Experience() {
  const [selectedExp, setSelectedExp] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [visibleExperiences, setVisibleExperiences] = useState(4);
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  const { getContent, updateContent } = useEditStore();

  const openModal = (index: number) => {
    setSelectedExp(index);
    setIsModalOpen(true);
  };

  const loadMore = () => {
    setVisibleExperiences(prev => Math.min(prev + 4, experiences.length));
  };

  return (
    <section id="experience" className="py-32 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold gradient-text mb-4">Professional Experience</h2>
          <EditableContent
            content={getContent('experience.description', 'Two decades of leadership, innovation, and technical excellence across global organizations')}
            onSave={(content) => updateContent('experience.description', content)}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
            label="Section Description"
          />
        </motion.div>
        
        <div className="space-y-12" ref={ref}>
          {experiences.slice(0, visibleExperiences).map((exp, index) => (
            <motion.div
              key={exp.title + exp.company}
              initial={{ opacity: 0, x: -20 }}
              animate={inView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="glass-effect rounded-xl p-8 card-hover border border-primary-100"
            >
              <div className="flex flex-col lg:flex-row justify-between mb-8">
                <div className="space-y-3">
                  <EditableContent
                    content={getContent(`experience.${index}.title`, exp.title)}
                    onSave={(content) => updateContent(`experience.${index}.title`, content)}
                    className="text-2xl font-bold gradient-text"
                    label="Job Title"
                  />
                  <div className="flex flex-wrap items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Building className="w-5 h-5 text-primary-600" />
                      <EditableContent
                        content={getContent(`experience.${index}.company`, exp.company)}
                        onSave={(content) => updateContent(`experience.${index}.company`, content)}
                        className="text-primary-600 font-medium"
                        label="Company Name"
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-5 h-5 text-gray-500" />
                      <EditableContent
                        content={getContent(`experience.${index}.location`, exp.location)}
                        onSave={(content) => updateContent(`experience.${index}.location`, content)}
                        className="text-gray-500"
                        label="Location"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between gap-4 mt-4 lg:mt-0">
                  <div className="flex items-center gap-2 text-gray-500">
                    <Calendar className="w-5 h-5" />
                    <EditableContent
                      content={getContent(`experience.${index}.period`, exp.period)}
                      onSave={(content) => updateContent(`experience.${index}.period`, content)}
                      className="text-gray-500"
                      label="Time Period"
                    />
                  </div>
                  <ViewMoreButton onClick={() => openModal(index)} />
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-center gap-2 text-gray-600 bg-primary-50 p-4 rounded-lg">
                  <Package className="w-5 h-5 text-primary-600 flex-shrink-0" />
                  <EditableContent
                    content={getContent(`experience.${index}.products`, exp.products)}
                    onSave={(content) => updateContent(`experience.${index}.products`, content)}
                    className="font-medium"
                    label="Products"
                  />
                </div>
                
                <div className="grid md:grid-cols-2 gap-6">
                  {exp.achievements.slice(0, 2).map((achievement, i) => (
                    <motion.div
                      key={achievement}
                      initial={{ opacity: 0, y: 10 }}
                      animate={inView ? { opacity: 1, y: 0 } : {}}
                      transition={{ duration: 0.3, delay: i * 0.1 }}
                      className="flex items-start gap-3 p-4 rounded-lg hover:bg-primary-50 transition-colors"
                    >
                      <CheckCircle className="w-5 h-5 text-primary-600 mt-1 flex-shrink-0" />
                      <EditableContent
                        content={getContent(`experience.${index}.achievements.${i}`, achievement)}
                        onSave={(content) => updateContent(`experience.${index}.achievements.${i}`, content)}
                        className="text-gray-600"
                        label={`Achievement ${i + 1}`}
                      />
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {visibleExperiences < experiences.length && (
          <div className="mt-12 text-center">
            <button
              onClick={loadMore}
              className="button-primary inline-flex items-center gap-2"
            >
              Load More Experience
              <Link className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={selectedExp !== null ? experiences[selectedExp].title : ''}
      >
        {selectedExp !== null && (
          <div className="space-y-8">
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <Building className="w-5 h-5 text-primary-600" />
                <EditableContent
                  content={getContent(`experience.${selectedExp}.company`, experiences[selectedExp].company)}
                  onSave={(content) => updateContent(`experience.${selectedExp}.company`, content)}
                  className="text-primary-600 font-medium"
                  label="Company Name"
                />
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5 text-gray-500" />
                <EditableContent
                  content={getContent(`experience.${selectedExp}.location`, experiences[selectedExp].location)}
                  onSave={(content) => updateContent(`experience.${selectedExp}.location`, content)}
                  className="text-gray-500"
                  label="Location"
                />
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <EditableContent
                  content={getContent(`experience.${selectedExp}.period`, experiences[selectedExp].period)}
                  onSave={(content) => updateContent(`experience.${selectedExp}.period`, content)}
                  className="text-gray-500"
                  label="Time Period"
                />
              </div>
            </div>

            <div className="bg-primary-50 p-6 rounded-xl">
              <h4 className="text-lg font-semibold mb-4">Products & Systems</h4>
              <div className="flex items-start gap-3">
                <Package className="w-5 h-5 text-primary-600 mt-1" />
                <EditableContent
                  content={getContent(`experience.${selectedExp}.products`, experiences[selectedExp].products)}
                  onSave={(content) => updateContent(`experience.${selectedExp}.products`, content)}
                  className="text-gray-700"
                  label="Products"
                />
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Key Achievements</h4>
              <div className="grid gap-4">
                {experiences[selectedExp].achievements.map((achievement, i) => (
                  <div
                    key={achievement}
                    className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg"
                  >
                    <CheckCircle className="w-5 h-5 text-primary-600 mt-1" />
                    <EditableContent
                      content={getContent(`experience.${selectedExp}.achievements.${i}`, achievement)}
                      onSave={(content) => updateContent(`experience.${selectedExp}.achievements.${i}`, content)}
                      className="text-gray-700"
                      label={`Achievement ${i + 1}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </section>
  );
}