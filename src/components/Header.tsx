import React, { useState, useEffect } from 'react';
import { Menu, X, Linkedin, Mail, Github, Award, Brain, Target, AlertTriangle, BarChart2 } from 'lucide-react';

const navItems = [
  { name: 'About', icon: Award, href: '#about' },
  { name: 'Skills', icon: Brain, href: '#skills' },
  { name: 'Experience', icon: Target, href: '#experience' },
  { name: 'Metrics', icon: BarChart2, href: '#metrics' },
  { name: 'Challenges', icon: AlertTriangle, href: '#challenges' },
  { name: 'Patents', icon: Award, href: '#patents' },
  { name: 'Education', icon: Award, href: '#education' }
];

interface HeaderProps {
  className?: string;
}

export default function Header({ className = '' }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 32); // Adjust scroll threshold to account for banner
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed w-full z-40 transition-all duration-300 ${
      isScrolled ? 'glass-effect shadow-lg' : 'bg-transparent'
    } ${className}`}>
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Rest of the header content remains the same */}
        <div className="flex items-center justify-between h-16">
          <a href="#top" className="text-xl sm:text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors">
            SG
          </a>
          
          <div className="hidden md:flex items-center gap-6 lg:gap-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="flex items-center gap-2 text-sm lg:text-base text-gray-600 hover:text-blue-600 transition-colors font-medium"
              >
                <item.icon className="w-4 h-4" />
                {item.name}
              </a>
            ))}
            
            <div className="flex items-center gap-3 lg:gap-4 ml-4">
              <a
                href="https://github.com/surenganne"
                className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github className="w-5 h-5" />
              </a>
              <a
                href="https://www.linkedin.com/in/surenganne/"
                className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
              >
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>
          
          <button
            className="md:hidden p-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>
        
        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 space-y-2">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="flex items-center gap-2 py-2 text-gray-600 hover:text-blue-600 transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <item.icon className="w-4 h-4" />
                {item.name}
              </a>
            ))}
            <div className="flex items-center gap-4 pt-4 border-t border-gray-100">
              <a
                href="https://github.com/surenganne"
                className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Github className="w-5 h-5" />
              </a>
              <a
                href="https://www.linkedin.com/in/surenganne/"
                className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}