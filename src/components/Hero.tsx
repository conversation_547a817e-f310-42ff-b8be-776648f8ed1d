import { motion } from 'framer-motion';
import { Award, Briefcase, Calendar, Clock, Mail, MapPin, Phone } from 'lucide-react';
import { useState } from 'react';
import { certifications } from '../data/skills';
import { useEditStore } from '../store/editStore';
import CompanyLogos from './CompanyLogos';
import EditableContent from './EditableContent';
import Modal from './Modal';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

export default function Hero() {
  const [isCalendarModalOpen, setCalendarModalOpen] = useState(false);
  const { getContent, updateContent } = useEditStore();

  const title = getContent('hero.title', 'Director of Software Engineering');
  const description = getContent('hero.description', 'Driving innovation through Generative AI, scaling engineering teams, and aligning technology with business goals for global impact.');
  const phone1 = getContent('hero.phone1', '+971 *********');
  const phone2 = getContent('hero.phone2', '+91 9966933336');
  const location = getContent('hero.location', 'Hyderabad | Dubai');

  return (
    <section id="about" className="relative min-h-[calc(100vh-4rem)] flex items-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-accent-50 px-4 sm:px-6">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary-100/30 via-transparent to-transparent opacity-70" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-accent-100/30 via-transparent to-transparent opacity-70" />
      <div className="absolute inset-0 grid-pattern opacity-10" />
      
      <div className="relative w-full max-w-7xl mx-auto py-12 sm:py-20">
        <motion.div 
          variants={container}
          initial="hidden"
          animate="show"
          className="flex flex-col items-center"
        >
          {/* Profile header - centered on all screens */}
          <motion.div variants={item} className="w-full max-w-4xl mx-auto mb-16 text-center">
            <div className="inline-block mb-3 px-4 py-1.5 bg-primary-50 rounded-full text-primary-700 text-sm font-medium">
              Entrepreneur In Residence (DXB2)
            </div>
            
            <div className="relative w-32 h-32 sm:w-40 sm:h-40 mx-auto mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-200 to-accent-200 rounded-full transform rotate-6 blur-[2px]" />
              <img 
                src="/images/image_suga.jpg"
                alt="Surendra Ganne"
                className="relative z-10 w-full h-full object-cover rounded-full shadow-xl border-4 border-white"
              />
            </div>
            
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
              Surendra Ganne
            </h1>
            
            <div className="flex items-center justify-center gap-2 mb-6">
              <Award className="w-5 h-5 sm:w-6 sm:h-6 text-primary-600" />
              <EditableContent
                content={title}
                onSave={(content) => updateContent('hero.title', content)}
                className="text-lg sm:text-xl text-primary-600 font-medium"
              />
            </div>
            
            <div className="max-w-2xl mx-auto">
              <EditableContent
                content={description}
                onSave={(content) => updateContent('hero.description', content)}
                className="text-base sm:text-lg md:text-xl text-gray-600 leading-relaxed"
                multiline
              />
            </div>
          </motion.div>
          
          {/* Three column layout for content */}
          <motion.div variants={item} className="w-full grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {/* Left column - Certifications */}
            <div className="glass-effect rounded-2xl p-6 backdrop-blur-sm bg-white/70 flex flex-col h-full">
              <div className="flex items-center gap-2 mb-4">
                <Award className="w-5 h-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-800">Certifications</h3>
              </div>
              
              <div className="flex flex-col gap-3 mt-2">
                {certifications.map((cert, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-white/50 rounded-xl hover:bg-primary-50/50 transition-colors">
                    <cert.icon className="w-5 h-5 text-primary-600" />
                    <span className="text-gray-700 font-medium">{cert.name}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Middle column - Contact Information */}
            <div className="glass-effect rounded-2xl p-6 backdrop-blur-sm bg-white/70 flex flex-col h-full">
              <div className="flex items-center gap-2 mb-4">
                <Phone className="w-5 h-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-800">Contact Me</h3>
              </div>
              
              <div className="flex flex-col gap-5 mt-2">
                <div className="flex items-start gap-3">
                  <MapPin className="w-5 h-5 text-primary-600 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Location</p>
                    <EditableContent
                      content={location}
                      onSave={(content) => updateContent('hero.location', content)}
                      className="text-base font-medium text-gray-800"
                    />
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Phone className="w-5 h-5 text-primary-600 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Primary</p>
                    <EditableContent
                      content={phone1}
                      onSave={(content) => updateContent('hero.phone1', content)}
                      className="text-base font-medium text-gray-800"
                    />
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Phone className="w-5 h-5 text-primary-600 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Secondary</p>
                    <EditableContent
                      content={phone2}
                      onSave={(content) => updateContent('hero.phone2', content)}
                      className="text-base font-medium text-gray-800"
                    />
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3 mt-auto pt-4">
                  <a 
                    href="mailto:<EMAIL>"
                    className="button-primary flex items-center justify-center gap-2 text-sm py-2.5 px-4 w-full"
                  >
                    <Mail className="w-4 h-4" />
                    Email Me
                  </a>
                  <button 
                    onClick={() => setCalendarModalOpen(true)}
                    className="button-secondary flex items-center justify-center gap-2 text-sm py-2.5 px-4 w-full"
                  >
                    <Calendar className="w-4 h-4" />
                    Schedule Call
                  </button>
                </div>
              </div>
            </div>
            
            {/* Right column - Companies */}
            <div className="glass-effect rounded-2xl p-6 backdrop-blur-sm bg-white/70 flex flex-col h-full">
              <div className="flex items-center gap-2 mb-4">
                <Briefcase className="w-5 h-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-800">Worked With</h3>
              </div>
              
              <div className="flex-grow flex flex-col justify-center">
                <CompanyLogos />
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      <Modal
        isOpen={isCalendarModalOpen}
        onClose={() => setCalendarModalOpen(false)}
        title="Schedule a Call"
      >
        <div className="space-y-6">
          <p className="text-gray-600">Choose your preferred meeting duration:</p>
          <div className="grid sm:grid-cols-2 gap-4">
            <a
              href="https://tidycal.com/surendraganne/30-minute-meeting"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center gap-3 p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors"
            >
              <Clock className="w-5 h-5 text-primary-600" />
              <span className="font-medium">30 Minutes</span>
            </a>
            <a
              href="https://tidycal.com/surendraganne/60-minute-meeting"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center gap-3 p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors"
            >
              <Clock className="w-5 h-5 text-primary-600" />
              <span className="font-medium">60 Minutes</span>
            </a>
          </div>
        </div>
      </Modal>
    </section>
  );
}