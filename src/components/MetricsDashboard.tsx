import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Users, Target, UserCheck, ClipboardCheck, 
  Package, ArrowUpRight
} from 'lucide-react';
import { overallMetrics, companyMetrics } from '../data/metrics';
import CompanyMetrics from './CompanyMetrics';

const MetricCard = ({ icon: Icon, label, value, delay = 0 }: { 
  icon: any, 
  label: string, 
  value: string | number,
  delay?: number 
}) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ delay }}
      className="relative group overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl 
                 transition-all duration-300 transform hover:-translate-y-1"
    >
      <div className="absolute top-0 right-0 w-32 h-32 -mr-16 -mt-16 bg-primary-100 rounded-full opacity-10 
                    group-hover:scale-110 transition-transform duration-500" />
      <div className="relative p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="p-3 bg-primary-50 rounded-xl group-hover:bg-primary-100 transition-colors">
            <Icon className="w-6 h-6 text-primary-600" />
          </div>
          <span className="text-sm font-medium text-gray-600">{label}</span>
        </div>
        <div className="flex items-baseline justify-between">
          <div className="text-3xl font-bold text-gray-900">
            {value}
          </div>
          <ArrowUpRight className="w-5 h-5 text-primary-600 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      </div>
    </motion.div>
  );
};

export default function MetricsDashboard() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const [openCompany, setOpenCompany] = useState<string | null>(null);

  const toggleCompany = (company: string) => {
    setOpenCompany(openCompany === company ? null : company);
  };

  return (
    <section id="metrics" ref={ref} className="py-32 bg-gradient-to-br from-primary-50 via-white to-accent-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold gradient-text mb-4">Impact & Achievements</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Measurable results and key performance indicators across leadership roles
          </p>
        </motion.div>

        <div className="mb-16">
          <h3 className="text-2xl font-bold gradient-text mb-8">Overall Impact</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { icon: Users, label: "People Managed", value: overallMetrics.totalPeopleManaged },
              { icon: UserCheck, label: "Total Interviews", value: overallMetrics.totalInterviews },
              { icon: ClipboardCheck, label: "Performance Reviews", value: overallMetrics.performanceReviews },
              { icon: Package, label: "Projects Delivered", value: overallMetrics.projectsDelivered }
            ].map((metric, index) => (
              <MetricCard 
                key={metric.label}
                icon={metric.icon}
                label={metric.label}
                value={metric.value}
                delay={index * 0.1}
              />
            ))}
          </div>
        </div>

        <div className="space-y-8">
          <h3 className="text-2xl font-bold gradient-text mb-8">Company-wise Metrics</h3>
          {companyMetrics.map((company, index) => (
            <CompanyMetrics 
              key={company.company}
              metrics={company}
              isOpen={openCompany === company.company}
              onToggle={() => toggleCompany(company.company)}
              delay={index * 0.2}
            />
          ))}
        </div>
      </div>
    </section>
  );
}