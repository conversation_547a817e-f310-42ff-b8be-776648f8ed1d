import React from 'react';
import { FileText, Users } from 'lucide-react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const patents = [
  {
    title: "System and Method for Determining Genuineness Corresponding to a Work Experience of a Job Seeker",
    icon: FileText,
    description: "An innovative system that leverages AI and machine learning to verify and validate work experience claims, enhancing recruitment accuracy and reducing fraudulent applications."
  },
  {
    title: "System and Method for Evaluating a Video Resume and Conducting a Video Based Interview of a Job Seeker",
    icon: Users,
    description: "A comprehensive platform for conducting and analyzing video-based interviews, incorporating advanced analytics and AI-driven assessment tools."
  }
];

export default function Patents() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  return (
    <section id="patents" ref={ref} className="section-padding bg-gradient-to-br from-primary-50 via-white to-accent-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold gradient-text mb-4">Patents</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Innovative solutions in HR technology and recruitment, transforming how organizations hire and evaluate talent
          </p>
        </motion.div>
        
        <div className="grid md:grid-cols-2 gap-8">
          {patents.map((patent, index) => {
            const Icon = patent.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: index * 0.2 }}
                className="glass-effect rounded-xl p-8 card-hover border border-primary-100"
              >
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-primary-100 rounded-lg">
                    <Icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      {patent.title}
                    </h3>
                    <p className="text-gray-600">
                      {patent.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}