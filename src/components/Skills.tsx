import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { skillCategories } from '../data/skills';
import ViewMoreButton from './ViewMoreButton';
import Modal from './Modal';

export default function Skills() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);

  const openModal = (categoryIndex: number) => {
    setSelectedCategory(categoryIndex);
    setIsModalOpen(true);
  };

  return (
    <section id="skills" ref={ref} className="py-32 bg-gradient-to-br from-white to-primary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold gradient-text mb-4">Skills & Expertise</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Comprehensive technical expertise spanning AI, cloud architecture, and software development
          </p>
        </motion.div>

        <div className="space-y-16">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div 
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: categoryIndex * 0.2 }}
              className="glass-effect rounded-xl p-8 card-hover border border-primary-100"
            >
              <div className="flex justify-between items-center mb-8">
                <h3 className="text-2xl font-bold gradient-text">{category.title}</h3>
                <ViewMoreButton onClick={() => openModal(categoryIndex)} />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {category.skills.slice(0, 6).map((skill, skillIndex) => (
                  <motion.div 
                    key={skill.name}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={inView ? { opacity: 1, scale: 1 } : {}}
                    transition={{ delay: (categoryIndex * 0.2) + (skillIndex * 0.1) }}
                    className="flex items-center gap-4 p-4 bg-white/50 rounded-xl transition-all duration-300 
                             hover:bg-primary-50 hover:shadow-lg hover:-translate-y-1"
                  >
                    <div className="p-3 bg-primary-100 rounded-lg">
                      <skill.icon className="w-6 h-6 text-primary-600" />
                    </div>
                    <span className="text-gray-700 font-medium">{skill.name}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={selectedCategory !== null ? skillCategories[selectedCategory].title : ''}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {selectedCategory !== null &&
            skillCategories[selectedCategory].skills.map((skill) => (
              <div
                key={skill.name}
                className="flex items-center gap-4 p-4 bg-primary-50 rounded-xl"
              >
                <div className="p-3 bg-primary-100 rounded-lg">
                  <skill.icon className="w-6 h-6 text-primary-600" />
                </div>
                <span className="text-gray-700 font-medium">{skill.name}</span>
              </div>
            ))}
        </div>
      </Modal>
    </section>
  );
}