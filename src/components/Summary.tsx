import React from 'react';
import { Target, Rocket, Brain, Trophy, Code, Globe } from 'lucide-react';
import EditableContent from './EditableContent';
import { useEditStore } from '../store/editStore';

const valueProposition = [
  {
    icon: Brain,
    title: "AI & Innovation Leadership",
    description: "Drive 30% faster product delivery through AI integration and innovative solutions"
  },
  {
    icon: Code,
    title: "Product Development Expert",
    description: "15+ years building scalable web/mobile applications and end-to-end product development"
  },
  {
    icon: Globe,
    title: "Global Team Leadership",
    description: "Led 50+ engineers across 3 countries, boosting productivity by 40%"
  }
];

export default function Summary() {
  const { getContent, updateContent } = useEditStore();

  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Value Proposition</h2>
          <EditableContent
            content={getContent('summary.description', 'Product Development Expert | AI Innovation Leader | Global Team Builder specializing in scalable web & mobile applications, AI integration, and end-to-end product development')}
            onSave={(content) => updateContent('summary.description', content)}
            className="text-gray-600 max-w-3xl mx-auto leading-relaxed"
            multiline
          />
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {valueProposition.map((prop, index) => (
            <div
              key={prop.title}
              className="glass-effect rounded-xl p-8 card-hover"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-4 bg-primary-50 rounded-full">
                  <prop.icon className="w-8 h-8 text-primary-600" />
                </div>
                <EditableContent
                  content={getContent(`summary.prop.${index}.title`, prop.title)}
                  onSave={(content) => updateContent(`summary.prop.${index}.title`, content)}
                  className="text-xl font-bold text-gray-900"
                />
                <EditableContent
                  content={getContent(`summary.prop.${index}.description`, prop.description)}
                  onSave={(content) => updateContent(`summary.prop.${index}.description`, content)}
                  className="text-gray-600"
                  multiline
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}