import { ChevronRight } from 'lucide-react';

interface ViewMoreButtonProps {
  onClick: () => void;
  className?: string;
}

export default function ViewMoreButton({ onClick, className = '' }: ViewMoreButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center gap-1 sm:gap-2 text-sm sm:text-base text-primary-600 hover:text-primary-700 font-medium transition-colors ${className}`}
    >
      View More
      <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
    </button>
  );
}