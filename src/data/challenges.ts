export const challenges = [
  {
    company: "Kastech Software Solutions Group",
    challenges: [
      {
        title: "Rapid AI Integration",
        description: "Challenge: Integrating AI capabilities into existing products while maintaining stability.",
        solution: "Implemented phased approach with parallel systems, allowing gradual transition and thorough testing.",
        outcome: "Successfully integrated AI features with zero downtime and 30% performance improvement."
      },
      {
        title: "Global Team Coordination",
        description: "Challenge: Managing teams across multiple time zones and cultural contexts.",
        solution: "Established structured communication protocols and implemented async-first workflows.",
        outcome: "Improved team collaboration and reduced meeting overhead by 40%."
      }
    ]
  },
  {
    company: "Infinity Learn",
    challenges: [
      {
        title: "Platform Scalability",
        description: "Challenge: Scaling user base from 10k to 1M+ users while maintaining performance.",
        solution: "Implemented microservices architecture and optimized database queries.",
        outcome: "Achieved 99.9% uptime during 100x user growth."
      },
      {
        title: "Legacy System Migration",
        description: "Challenge: Modernizing legacy systems without disrupting operations.",
        solution: "Developed staged migration plan with fallback mechanisms.",
        outcome: "Completed migration with zero data loss and minimal downtime."
      }
    ]
  },
  {
    company: "Trice Systems",
    challenges: [
      {
        title: "Product Market Fit",
        description: "Challenge: Pivoting product strategy based on market feedback.",
        solution: "Implemented rapid prototyping and continuous user feedback loops.",
        outcome: "Achieved product-market fit within 6 months."
      },
      {
        title: "Resource Optimization",
        description: "Challenge: Managing resource constraints while maintaining quality.",
        solution: "Introduced automated testing and CI/CD pipelines.",
        outcome: "Reduced development cycle time by 40%."
      }
    ]
  }
];