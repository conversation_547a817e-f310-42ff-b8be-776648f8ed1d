export const overallMetrics = {
  totalPeopleManaged: "250+",
  totalInterviews: "1500+",
  totalHires: "350+",
  performanceReviews: "450+",
  projectsDelivered: "60+",
  patentsFiled: 2,
  teamRetentionRate: "92%",
  avgProductivityIncrease: "40%"
};

export const companyMetrics = [
  {
    company: "Kastech Software Solutions Group",
    period: "2023-Present",
    metrics: {
      teamSize: "50",
      interviews: "150",
      hires: "45",
      performanceReviews: "70",
      kras: [
        "Drive AI integration across products",
        "Scale engineering teams globally",
        "Optimize resource allocation",
        "Improve code quality metrics",
        "Enhance delivery timelines"
      ]
    }
  },
  {
    company: "Infinity Learn",
    period: "2022-2023",
    metrics: {
      teamSize: "50",
      interviews: "150",
      hires: "30",
      performanceReviews: "70",
      kras: [
        "Scale UAM platform",
        "Enhance system stability",
        "Improve architecture",
        "Drive agile transformation",
        "Optimize development processes"
      ]
    }
  },
  {
    company: "Trice Systems",
    period: "2019-2022",
    metrics: {
      teamSize: "12",
      interviews: "100",
      hires: "9",
      performanceReviews: "100",
      kras: [
        "Lead product development",
        "Implement microservices",
        "Drive mobile app development",
        "Enhance team capabilities",
        "Improve delivery processes"
      ]
    }
  }
];