@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem;
    -webkit-tap-highlight-color: transparent;
  }
  
  body {
    @apply text-gray-900 bg-gradient-to-br from-primary-50 to-accent-50 antialiased min-h-screen;
  }

  /* Improve touch targets on mobile */
  button, a {
    @apply cursor-pointer min-h-[44px] min-w-[44px] flex items-center justify-center;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }
  
  .animate-slide-in {
    animation: slideIn 0.6s ease-out forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .glass-effect {
    @apply bg-white/70 backdrop-blur-lg shadow-xl shadow-primary-100/20;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-2xl hover:shadow-primary-100/30 hover:-translate-y-1;
  }

  .section-padding {
    @apply py-16 sm:py-24 md:py-32;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600;
  }

  .grid-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgb(226 232 240 / 0.3) 1px, transparent 0);
    background-size: 24px 24px;
  }
}

.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Custom styles for sections */
.section-bg-primary {
  @apply bg-gradient-to-br from-primary-50 via-white to-accent-50;
}

.section-bg-secondary {
  @apply bg-gradient-to-br from-accent-50 via-white to-primary-50;
}

.icon-box {
  @apply p-2 sm:p-3 rounded-lg bg-gradient-to-br from-primary-100 to-accent-100 text-primary-600;
}

.button-primary {
  @apply px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-primary-600 to-accent-600 text-white rounded-lg 
  hover:from-primary-700 hover:to-accent-700 transition-all duration-300 shadow-lg shadow-primary-200/50
  text-sm sm:text-base;
}

.button-secondary {
  @apply px-4 py-2 sm:px-6 sm:py-3 bg-white text-primary-600 rounded-lg hover:bg-primary-50 transition-colors 
  border border-primary-200 shadow-lg shadow-primary-100/30 text-sm sm:text-base;
}

.heading-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 font-bold;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .glass-effect {
    @apply shadow-md;
  }

  .card-hover:hover {
    transform: none;
  }
}