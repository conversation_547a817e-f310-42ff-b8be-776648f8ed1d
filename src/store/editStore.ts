import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface EditState {
  edits: Record<string, any>;
  updateContent: (key: string, content: any) => void;
  getContent: (key: string, defaultContent: any) => any;
}

export const useEditStore = create<EditState>()(
  persist(
    (set, get) => ({
      edits: {},
      updateContent: (key, content) => set((state) => ({
        edits: { ...state.edits, [key]: content }
      })),
      getContent: (key, defaultContent) => defaultContent
    }),
    {
      name: 'content-storage'
    }
  )
);